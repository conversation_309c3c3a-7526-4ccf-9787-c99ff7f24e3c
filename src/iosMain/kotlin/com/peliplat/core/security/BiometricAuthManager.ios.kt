package com.peliplat.core.security

import com.peliplat.feature.auth.domain.model.BiometricResult
import kotlinx.cinterop.*
import kotlinx.coroutines.suspendCancellableCoroutine
import platform.Foundation.*
import platform.LocalAuthentication.*
import kotlin.coroutines.resume

/**
 * iOS implementation of BiometricAuthManager using LocalAuthentication
 */
actual class BiometricAuthManager(
    private val secureStorage: SecureStorage
) {
    
    private val context = LAContext()
    
    actual fun isAvailable(): Boolean {
        memScoped {
            val error = alloc<ObjCObjectVar<NSError?>>()
            val available = context.canEvaluatePolicy(LAPolicyDeviceOwnerAuthenticationWithBiometrics, error.ptr)
            return available
        }
    }
    
    actual fun isEnrolled(): Boolean {
        return isAvailable() // On iOS, if biometrics are available, they are enrolled
    }
    
    actual suspend fun authenticate(
        title: String,
        subtitle: String,
        description: String
    ): BiometricResult = suspendCancellableCoroutine { continuation ->
        
        if (!isAvailable()) {
            continuation.resume(BiometricResult.NotAvailable)
            return@suspendCancellableCoroutine
        }
        
        val reason = "$title\n$subtitle\n$description"
        
        context.evaluatePolicy(
            LAPolicyDeviceOwnerAuthenticationWithBiometrics,
            reason
        ) { success, error ->
            if (success) {
                continuation.resume(BiometricResult.Success)
            } else {
                error?.let { nsError ->
                    val errorCode = nsError.code
                    when (errorCode) {
                        LAErrorUserCancel.toLong() -> {
                            continuation.resume(BiometricResult.Cancelled)
                        }
                        LAErrorBiometryNotAvailable.toLong() -> {
                            continuation.resume(BiometricResult.NotAvailable)
                        }
                        LAErrorBiometryNotEnrolled.toLong() -> {
                            continuation.resume(BiometricResult.NotEnrolled)
                        }
                        LAErrorAuthenticationFailed.toLong() -> {
                            continuation.resume(BiometricResult.Failed)
                        }
                        else -> {
                            continuation.resume(BiometricResult.Error(nsError.localizedDescription))
                        }
                    }
                } ?: continuation.resume(BiometricResult.Failed)
            }
        }
    }
    
    actual suspend fun isBiometricEnabledForApp(): Boolean {
        return secureStorage.get(SecureStorageKeys.BIOMETRIC_ENABLED) == "true"
    }
    
    actual suspend fun enableBiometricForApp() {
        secureStorage.store(SecureStorageKeys.BIOMETRIC_ENABLED, "true")
    }
    
    actual suspend fun disableBiometricForApp() {
        secureStorage.remove(SecureStorageKeys.BIOMETRIC_ENABLED)
    }
}
