package com.peliplat.core.security

import kotlinx.cinterop.*
import platform.Foundation.*
import platform.Security.*

/**
 * iOS implementation of SecureStorage using Keychain Services
 */
actual class SecureStorage {
    
    private val serviceName = "com.peliplat.app"
    
    actual suspend fun store(key: String, value: String) {
        // First, try to update existing item
        val updateQuery = CFDictionaryCreateMutable(null, 0, null, null)
        CFDictionaryAddValue(updateQuery, kSecClass, kSecClassGenericPassword)
        CFDictionaryAddValue(updateQuery, kSecAttrService, CFStringCreateWithCString(null, serviceName, kCFStringEncodingUTF8))
        CFDictionaryAddValue(updateQuery, kSecAttrAccount, CFStringCreateWithCString(null, key, kCFStringEncodingUTF8))
        
        val updateAttributes = CFDictionaryCreateMutable(null, 0, null, null)
        val valueData = value.encodeToByteArray()
        val cfData = CFDataCreate(null, valueData.refTo(0), valueData.size.toLong())
        CFDictionaryAddValue(updateAttributes, kSecValueData, cfData)
        
        val updateStatus = SecItemUpdate(updateQuery, updateAttributes)
        
        if (updateStatus == errSecItemNotFound) {
            // Item doesn't exist, create new one
            val addQuery = CFDictionaryCreateMutable(null, 0, null, null)
            CFDictionaryAddValue(addQuery, kSecClass, kSecClassGenericPassword)
            CFDictionaryAddValue(addQuery, kSecAttrService, CFStringCreateWithCString(null, serviceName, kCFStringEncodingUTF8))
            CFDictionaryAddValue(addQuery, kSecAttrAccount, CFStringCreateWithCString(null, key, kCFStringEncodingUTF8))
            CFDictionaryAddValue(addQuery, kSecValueData, cfData)
            CFDictionaryAddValue(addQuery, kSecAttrAccessible, kSecAttrAccessibleWhenUnlockedThisDeviceOnly)
            
            SecItemAdd(addQuery, null)
        }
    }
    
    actual suspend fun get(key: String): String? {
        val query = CFDictionaryCreateMutable(null, 0, null, null)
        CFDictionaryAddValue(query, kSecClass, kSecClassGenericPassword)
        CFDictionaryAddValue(query, kSecAttrService, CFStringCreateWithCString(null, serviceName, kCFStringEncodingUTF8))
        CFDictionaryAddValue(query, kSecAttrAccount, CFStringCreateWithCString(null, key, kCFStringEncodingUTF8))
        CFDictionaryAddValue(query, kSecReturnData, kCFBooleanTrue)
        CFDictionaryAddValue(query, kSecMatchLimit, kSecMatchLimitOne)
        
        memScoped {
            val result = alloc<CFTypeRefVar>()
            val status = SecItemCopyMatching(query, result.ptr)
            
            if (status == errSecSuccess) {
                val data = result.value as CFDataRef
                val length = CFDataGetLength(data).toInt()
                val bytes = CFDataGetBytePtr(data)
                val byteArray = ByteArray(length) { bytes!![it] }
                return byteArray.decodeToString()
            }
        }
        
        return null
    }
    
    actual suspend fun remove(key: String) {
        val query = CFDictionaryCreateMutable(null, 0, null, null)
        CFDictionaryAddValue(query, kSecClass, kSecClassGenericPassword)
        CFDictionaryAddValue(query, kSecAttrService, CFStringCreateWithCString(null, serviceName, kCFStringEncodingUTF8))
        CFDictionaryAddValue(query, kSecAttrAccount, CFStringCreateWithCString(null, key, kCFStringEncodingUTF8))
        
        SecItemDelete(query)
    }
    
    actual suspend fun clear() {
        val query = CFDictionaryCreateMutable(null, 0, null, null)
        CFDictionaryAddValue(query, kSecClass, kSecClassGenericPassword)
        CFDictionaryAddValue(query, kSecAttrService, CFStringCreateWithCString(null, serviceName, kCFStringEncodingUTF8))
        
        SecItemDelete(query)
    }
    
    actual suspend fun contains(key: String): Boolean {
        return get(key) != null
    }
}
