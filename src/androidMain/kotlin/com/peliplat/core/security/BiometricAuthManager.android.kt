package com.peliplat.core.security

import android.content.Context
import androidx.biometric.BiometricManager
import androidx.biometric.BiometricPrompt
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import com.peliplat.feature.auth.domain.model.BiometricResult
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume

/**
 * Android implementation of BiometricAuthManager using AndroidX Biometric
 */
actual class BiometricAuthManager(
    private val context: Context,
    private val secureStorage: SecureStorage
) {
    
    actual fun isAvailable(): Bo<PERSON>an {
        val biometricManager = BiometricManager.from(context)
        return when (biometricManager.canAuthenticate(BiometricManager.Authenticators.BIOMETRIC_WEAK)) {
            BiometricManager.BIOMETRIC_SUCCESS -> true
            else -> false
        }
    }
    
    actual fun isEnrolled(): <PERSON><PERSON><PERSON> {
        val biometricManager = BiometricManager.from(context)
        return biometricManager.canAuthenticate(BiometricManager.Authenticators.BIOMETRIC_WEAK) == BiometricManager.BIOMETRIC_SUCCESS
    }
    
    actual suspend fun authenticate(
        title: String,
        subtitle: String,
        description: String
    ): BiometricResult = suspendCancellableCoroutine { continuation ->
        
        if (!isAvailable()) {
            continuation.resume(BiometricResult.NotAvailable)
            return@suspendCancellableCoroutine
        }
        
        if (!isEnrolled()) {
            continuation.resume(BiometricResult.NotEnrolled)
            return@suspendCancellableCoroutine
        }
        
        val activity = context as? FragmentActivity
        if (activity == null) {
            continuation.resume(BiometricResult.Error("Activity context required"))
            return@suspendCancellableCoroutine
        }
        
        val executor = ContextCompat.getMainExecutor(context)
        val biometricPrompt = BiometricPrompt(activity, executor,
            object : BiometricPrompt.AuthenticationCallback() {
                override fun onAuthenticationError(errorCode: Int, errString: CharSequence) {
                    super.onAuthenticationError(errorCode, errString)
                    when (errorCode) {
                        BiometricPrompt.ERROR_USER_CANCELED,
                        BiometricPrompt.ERROR_CANCELED -> {
                            continuation.resume(BiometricResult.Cancelled)
                        }
                        else -> {
                            continuation.resume(BiometricResult.Error(errString.toString()))
                        }
                    }
                }
                
                override fun onAuthenticationSucceeded(result: BiometricPrompt.AuthenticationResult) {
                    super.onAuthenticationSucceeded(result)
                    continuation.resume(BiometricResult.Success)
                }
                
                override fun onAuthenticationFailed() {
                    super.onAuthenticationFailed()
                    continuation.resume(BiometricResult.Failed)
                }
            })
        
        val promptInfo = BiometricPrompt.PromptInfo.Builder()
            .setTitle(title)
            .setSubtitle(subtitle)
            .setDescription(description)
            .setNegativeButtonText("Cancel")
            .build()
        
        biometricPrompt.authenticate(promptInfo)
        
        continuation.invokeOnCancellation {
            biometricPrompt.cancelAuthentication()
        }
    }
    
    actual suspend fun isBiometricEnabledForApp(): Boolean {
        return secureStorage.get(SecureStorageKeys.BIOMETRIC_ENABLED) == "true"
    }
    
    actual suspend fun enableBiometricForApp() {
        secureStorage.store(SecureStorageKeys.BIOMETRIC_ENABLED, "true")
    }
    
    actual suspend fun disableBiometricForApp() {
        secureStorage.remove(SecureStorageKeys.BIOMETRIC_ENABLED)
    }
}
