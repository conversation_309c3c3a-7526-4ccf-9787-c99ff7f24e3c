package com.peliplat.core.security

import android.content.Context
import android.content.SharedPreferences
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey

/**
 * Android implementation of SecureStorage using EncryptedSharedPreferences
 */
actual class SecureStorage(private val context: Context) {
    
    private val masterKey = MasterKey.Builder(context)
        .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
        .build()
    
    private val sharedPreferences: SharedPreferences = EncryptedSharedPreferences.create(
        context,
        "peliplat_secure_prefs",
        masterKey,
        EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
        EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
    )
    
    actual suspend fun store(key: String, value: String) {
        sharedPreferences.edit().putString(key, value).apply()
    }
    
    actual suspend fun get(key: String): String? {
        return sharedPreferences.getString(key, null)
    }
    
    actual suspend fun remove(key: String) {
        sharedPreferences.edit().remove(key).apply()
    }
    
    actual suspend fun clear() {
        sharedPreferences.edit().clear().apply()
    }
    
    actual suspend fun contains(key: String): Boolean {
        return sharedPreferences.contains(key)
    }
}
