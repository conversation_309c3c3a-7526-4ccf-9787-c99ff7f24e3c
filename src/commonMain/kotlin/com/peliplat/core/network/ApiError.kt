package com.peliplat.core.network

import kotlinx.serialization.Serializable

/**
 * Represents different types of API errors
 */
sealed class ApiError(
    open val message: String,
    open val code: Int = ApiConfig.ErrorCodes.UNKNOWN_ERROR
) {
    
    data class NetworkError(
        override val message: String = "Network connection failed"
    ) : ApiError(message, ApiConfig.ErrorCodes.NETWORK_ERROR)
    
    data class TimeoutError(
        override val message: String = "Request timed out"
    ) : ApiError(message, ApiConfig.ErrorCodes.TIMEOUT_ERROR)
    
    data class UnauthorizedError(
        override val message: String = "Authentication required"
    ) : ApiError(message, ApiConfig.ErrorCodes.UNAUTHORIZED)
    
    data class ForbiddenError(
        override val message: String = "Access forbidden"
    ) : ApiError(message, ApiConfig.ErrorCodes.FORBIDDEN)
    
    data class NotFoundError(
        override val message: String = "Resource not found"
    ) : ApiError(message, ApiConfig.ErrorCodes.NOT_FOUND)
    
    data class ValidationError(
        override val message: String = "Validation failed",
        val errors: Map<String, List<String>> = emptyMap()
    ) : ApiError(message, ApiConfig.ErrorCodes.VALIDATION_ERROR)
    
    data class ServerError(
        override val message: String = "Server error occurred"
    ) : ApiError(message, ApiConfig.ErrorCodes.SERVER_ERROR)
    
    data class UnknownError(
        override val message: String = "An unknown error occurred",
        override val code: Int = ApiConfig.ErrorCodes.UNKNOWN_ERROR
    ) : ApiError(message, code)
}

/**
 * Represents the result of an API operation
 */
sealed class ApiResult<out T> {
    data class Success<T>(val data: T) : ApiResult<T>()
    data class Error(val error: ApiError) : ApiResult<Nothing>()
    
    inline fun <R> map(transform: (T) -> R): ApiResult<R> {
        return when (this) {
            is Success -> Success(transform(data))
            is Error -> this
        }
    }
    
    inline fun onSuccess(action: (T) -> Unit): ApiResult<T> {
        if (this is Success) action(data)
        return this
    }
    
    inline fun onError(action: (ApiError) -> Unit): ApiResult<T> {
        if (this is Error) action(error)
        return this
    }
    
    fun getOrNull(): T? = when (this) {
        is Success -> data
        is Error -> null
    }
    
    fun getOrThrow(): T = when (this) {
        is Success -> data
        is Error -> throw ApiException(error)
    }
}

/**
 * Exception wrapper for API errors
 */
class ApiException(val apiError: ApiError) : Exception(apiError.message)

/**
 * Standard API response format
 */
@Serializable
data class ApiResponse<T>(
    val success: Boolean,
    val data: T? = null,
    val message: String? = null,
    val errors: Map<String, List<String>>? = null,
    val code: Int? = null
)

/**
 * Paginated response format
 */
@Serializable
data class PaginatedResponse<T>(
    val items: List<T>,
    val totalCount: Int,
    val page: Int,
    val pageSize: Int,
    val totalPages: Int = (totalCount + pageSize - 1) / pageSize,
    val hasNext: Boolean = page < totalPages,
    val hasPrevious: Boolean = page > 1
)

/**
 * Error response format from server
 */
@Serializable
data class ErrorResponse(
    val message: String,
    val errors: Map<String, List<String>>? = null,
    val code: Int? = null
)
