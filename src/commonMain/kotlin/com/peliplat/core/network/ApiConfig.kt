package com.peliplat.core.network

/**
 * API configuration for production and development environments
 */
object ApiConfig {
    
    // Environment configuration
    const val IS_PRODUCTION = false // Set to true for production builds
    
    // Base URLs
    const val PRODUCTION_BASE_URL = "https://api.peliplat.com"
    const val DEVELOPMENT_BASE_URL = "https://api.peliplat.com" // Same for now
    
    val baseUrl: String
        get() = if (IS_PRODUCTION) PRODUCTION_BASE_URL else DEVELOPMENT_BASE_URL
    
    // Timeouts (in milliseconds)
    const val CONNECT_TIMEOUT = 30_000L
    const val REQUEST_TIMEOUT = 60_000L
    const val SOCKET_TIMEOUT = 60_000L
    
    // Headers
    const val USER_AGENT = "Peliplat-Mobile/1.0.0"
    const val API_VERSION = "v1"
    
    // Content types
    object ContentTypes {
        const val JSON = "application/json"
        const val FORM_DATA = "multipart/form-data"
    }
    
    // API Endpoints
    object Endpoints {
        // Authentication
        const val LOGIN = "/auth/login"
        const val REGISTER = "/auth/register"
        const val REFRESH_TOKEN = "/auth/refresh"
        const val LOGOUT = "/auth/logout"
        const val FORGOT_PASSWORD = "/auth/forgot-password"
        const val RESET_PASSWORD = "/auth/reset-password"
        const val VERIFY_EMAIL = "/auth/verify-email"
        
        // User
        const val USER_PROFILE = "/user/profile"
        const val USER_PREFERENCES = "/user/preferences"
        const val USER_AVATAR = "/user/avatar"
        
        // Articles
        const val ARTICLES = "/articles"
        const val ARTICLE_BY_ID = "/articles/{id}"
        const val ARTICLE_COMMENTS = "/articles/{id}/comments"
        const val ARTICLE_LIKE = "/articles/{id}/like"
        const val ARTICLE_BOOKMARK = "/articles/{id}/bookmark"
        
        // Comments
        const val COMMENTS = "/comments"
        const val COMMENT_BY_ID = "/comments/{id}"
        const val COMMENT_LIKE = "/comments/{id}/like"
        
        // Discussions
        const val DISCUSSIONS = "/discussions"
        const val DISCUSSION_BY_ID = "/discussions/{id}"
        const val DISCUSSION_REPLIES = "/discussions/{id}/replies"
        
        // Media
        const val MEDIA_UPLOAD = "/media/upload"
        const val MEDIA_BY_ID = "/media/{id}"
        
        // Search
        const val SEARCH = "/search"
        const val SEARCH_ARTICLES = "/search/articles"
        const val SEARCH_USERS = "/search/users"
        
        // Social
        const val FOLLOW_USER = "/social/follow/{userId}"
        const val UNFOLLOW_USER = "/social/unfollow/{userId}"
        const val FOLLOWERS = "/social/followers"
        const val FOLLOWING = "/social/following"
        
        // Notifications
        const val NOTIFICATIONS = "/notifications"
        const val MARK_NOTIFICATION_READ = "/notifications/{id}/read"
    }
    
    // Error codes
    object ErrorCodes {
        const val UNAUTHORIZED = 401
        const val FORBIDDEN = 403
        const val NOT_FOUND = 404
        const val VALIDATION_ERROR = 422
        const val SERVER_ERROR = 500
        const val NETWORK_ERROR = -1
        const val TIMEOUT_ERROR = -2
        const val UNKNOWN_ERROR = -999
    }
    
    // Pagination defaults
    object Pagination {
        const val DEFAULT_PAGE_SIZE = 20
        const val MAX_PAGE_SIZE = 100
        const val DEFAULT_PAGE = 1
    }
    
    // Cache settings
    object Cache {
        const val ARTICLES_CACHE_DURATION = 5 * 60 * 1000L // 5 minutes
        const val USER_CACHE_DURATION = 10 * 60 * 1000L // 10 minutes
        const val COMMENTS_CACHE_DURATION = 2 * 60 * 1000L // 2 minutes
    }
    
    // Development settings
    object Development {
        const val ENABLE_LOGGING = true
        const val ENABLE_MOCK_FALLBACK = true
        const val MOCK_DELAY_MS = 500L // Simulate network delay
    }
}
