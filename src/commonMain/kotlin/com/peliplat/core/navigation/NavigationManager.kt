package com.peliplat.core.navigation

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * Navigation destinations for the app
 */
sealed class Destination(val route: String) {
    // Authentication
    object Login : Destination("login")
    object Register : Destination("register")
    object ForgotPassword : Destination("forgot_password")
    
    // Main App
    object Home : Destination("home")
    object Articles : Destination("articles")
    object Discussions : Destination("discussions")
    object Library : Destination("library")
    object Challenge : Destination("challenge")
    object Profile : Destination("profile")
    
    // Detail screens
    data class ArticleDetail(val articleId: String) : Destination("article_detail/$articleId")
    data class UserProfile(val userId: String) : Destination("user_profile/$userId")
    data class DiscussionDetail(val discussionId: String) : Destination("discussion_detail/$discussionId")
    
    // Settings
    object Settings : Destination("settings")
    object EditProfile : Destination("edit_profile")
    object Preferences : Destination("preferences")
    
    companion object {
        fun fromRoute(route: String): Destination? {
            return when {
                route == Login.route -> Login
                route == Register.route -> Register
                route == ForgotPassword.route -> ForgotPassword
                route == Home.route -> Home
                route == Articles.route -> Articles
                route == Discussions.route -> Discussions
                route == Library.route -> Library
                route == Challenge.route -> Challenge
                route == Profile.route -> Profile
                route == Settings.route -> Settings
                route == EditProfile.route -> EditProfile
                route == Preferences.route -> Preferences
                route.startsWith("article_detail/") -> {
                    val articleId = route.removePrefix("article_detail/")
                    ArticleDetail(articleId)
                }
                route.startsWith("user_profile/") -> {
                    val userId = route.removePrefix("user_profile/")
                    UserProfile(userId)
                }
                route.startsWith("discussion_detail/") -> {
                    val discussionId = route.removePrefix("discussion_detail/")
                    DiscussionDetail(discussionId)
                }
                else -> null
            }
        }
    }
}

/**
 * Navigation state for managing app navigation
 */
data class NavigationState(
    val currentDestination: Destination = Destination.Login,
    val canGoBack: Boolean = false,
    val isAuthenticated: Boolean = false
)

/**
 * Cross-platform navigation manager
 */
class NavigationManager {
    
    private val _navigationState = MutableStateFlow(NavigationState())
    val navigationState: StateFlow<NavigationState> = _navigationState.asStateFlow()
    
    private val backStack = mutableListOf<Destination>()
    
    /**
     * Navigate to a destination
     */
    fun navigateTo(destination: Destination, clearBackStack: Boolean = false) {
        if (clearBackStack) {
            backStack.clear()
        } else {
            // Add current destination to back stack
            val current = _navigationState.value.currentDestination
            if (current != destination) {
                backStack.add(current)
            }
        }
        
        updateNavigationState(destination)
    }
    
    /**
     * Navigate back to previous destination
     */
    fun navigateBack(): Boolean {
        return if (backStack.isNotEmpty()) {
            val previousDestination = backStack.removeLastOrNull()
            if (previousDestination != null) {
                updateNavigationState(previousDestination)
                true
            } else {
                false
            }
        } else {
            false
        }
    }
    
    /**
     * Navigate to home and clear back stack
     */
    fun navigateToHome() {
        navigateTo(Destination.Home, clearBackStack = true)
    }
    
    /**
     * Navigate to login and clear back stack
     */
    fun navigateToLogin() {
        navigateTo(Destination.Login, clearBackStack = true)
    }
    
    /**
     * Set authentication state
     */
    fun setAuthenticated(isAuthenticated: Boolean) {
        _navigationState.value = _navigationState.value.copy(
            isAuthenticated = isAuthenticated
        )
        
        // If user logs out, navigate to login
        if (!isAuthenticated) {
            navigateToLogin()
        }
    }
    
    /**
     * Handle deep link navigation
     */
    fun handleDeepLink(url: String): Boolean {
        val destination = parseDeepLink(url)
        return if (destination != null) {
            navigateTo(destination)
            true
        } else {
            false
        }
    }
    
    /**
     * Get current destination
     */
    fun getCurrentDestination(): Destination {
        return _navigationState.value.currentDestination
    }
    
    /**
     * Check if user can navigate back
     */
    fun canGoBack(): Boolean {
        return _navigationState.value.canGoBack
    }
    
    private fun updateNavigationState(destination: Destination) {
        _navigationState.value = _navigationState.value.copy(
            currentDestination = destination,
            canGoBack = backStack.isNotEmpty()
        )
    }
    
    private fun parseDeepLink(url: String): Destination? {
        // Parse deep link URLs like:
        // peliplat://article/123
        // peliplat://user/456
        // peliplat://discussion/789
        
        if (!url.startsWith("peliplat://")) return null
        
        val path = url.removePrefix("peliplat://")
        val segments = path.split("/")
        
        return when (segments.firstOrNull()) {
            "article" -> segments.getOrNull(1)?.let { Destination.ArticleDetail(it) }
            "user" -> segments.getOrNull(1)?.let { Destination.UserProfile(it) }
            "discussion" -> segments.getOrNull(1)?.let { Destination.DiscussionDetail(it) }
            "home" -> Destination.Home
            "articles" -> Destination.Articles
            "discussions" -> Destination.Discussions
            "library" -> Destination.Library
            "challenge" -> Destination.Challenge
            "profile" -> Destination.Profile
            else -> null
        }
    }
}

/**
 * Navigation events for handling navigation actions
 */
sealed class NavigationEvent {
    object NavigateBack : NavigationEvent()
    data class NavigateTo(val destination: Destination) : NavigationEvent()
    data class HandleDeepLink(val url: String) : NavigationEvent()
}
