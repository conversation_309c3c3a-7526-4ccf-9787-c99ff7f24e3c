package com.peliplat.core.security

import com.peliplat.feature.auth.domain.model.TokenResponse
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

/**
 * Token manager for handling JWT tokens with automatic refresh
 */
class TokenManager(
    private val secureStorage: SecureStorage
) {
    private val refreshMutex = Mutex()
    private var refreshTokenCallback: (suspend (String) -> TokenResponse?)? = null
    
    /**
     * Set the callback for refreshing tokens
     */
    fun setRefreshTokenCallback(callback: suspend (String) -> TokenResponse?) {
        refreshTokenCallback = callback
    }
    
    /**
     * Store authentication tokens securely
     */
    suspend fun storeTokens(tokenResponse: TokenResponse) {
        secureStorage.store(SecureStorageKeys.ACCESS_TOKEN, tokenResponse.accessToken)
        secureStorage.store(SecureStorageKeys.REFRESH_TOKEN, tokenResponse.refreshToken)
        secureStorage.store(SecureStorageKeys.TOKEN_EXPIRY, tokenResponse.expiresAt.toString())
    }
    
    /**
     * Get a valid access token, refreshing if necessary
     */
    suspend fun getValidAccessToken(): String? {
        return refreshMutex.withLock {
            val accessToken = secureStorage.get(SecureStorageKeys.ACCESS_TOKEN)
            val expiryString = secureStorage.get(SecureStorageKeys.TOKEN_EXPIRY)

            if (accessToken == null) return@withLock null

            val expiry = expiryString?.toLongOrNull() ?: return@withLock null
            val currentTime = System.currentTimeMillis()

            // If token is still valid (with 5 minute buffer), return it
            if (expiry > currentTime + (5 * 60 * 1000)) {
                return@withLock accessToken
            }

            // Try to refresh the token
            val refreshToken = secureStorage.get(SecureStorageKeys.REFRESH_TOKEN)
            if (refreshToken != null && refreshTokenCallback != null) {
                try {
                    val newTokens = refreshTokenCallback!!.invoke(refreshToken)
                    if (newTokens != null) {
                        storeTokens(newTokens)
                        return@withLock newTokens.accessToken
                    }
                } catch (e: Exception) {
                    // Refresh failed, clear tokens
                    clearTokens()
                }
            }

            null
        }
    }
    
    /**
     * Get the current access token without validation
     */
    suspend fun getAccessToken(): String? {
        return secureStorage.get(SecureStorageKeys.ACCESS_TOKEN)
    }
    
    /**
     * Get the current refresh token
     */
    suspend fun getRefreshToken(): String? {
        return secureStorage.get(SecureStorageKeys.REFRESH_TOKEN)
    }
    
    /**
     * Check if tokens are stored
     */
    suspend fun hasTokens(): Boolean {
        return secureStorage.contains(SecureStorageKeys.ACCESS_TOKEN) &&
                secureStorage.contains(SecureStorageKeys.REFRESH_TOKEN)
    }
    
    /**
     * Check if the current token is valid (not expired)
     */
    suspend fun isTokenValid(): Boolean {
        val expiryString = secureStorage.get(SecureStorageKeys.TOKEN_EXPIRY) ?: return false
        val expiry = expiryString.toLongOrNull() ?: return false
        return expiry > System.currentTimeMillis()
    }
    
    /**
     * Clear all stored tokens
     */
    suspend fun clearTokens() {
        secureStorage.remove(SecureStorageKeys.ACCESS_TOKEN)
        secureStorage.remove(SecureStorageKeys.REFRESH_TOKEN)
        secureStorage.remove(SecureStorageKeys.TOKEN_EXPIRY)
    }
    
    /**
     * Get token expiry time
     */
    suspend fun getTokenExpiry(): Long? {
        return secureStorage.get(SecureStorageKeys.TOKEN_EXPIRY)?.toLongOrNull()
    }
}
