package com.peliplat.core.security

import com.peliplat.feature.auth.domain.model.BiometricResult

/**
 * Biometric authentication manager for platform-specific biometric authentication
 */
expect class BiometricAuthManager {
    
    /**
     * Check if biometric authentication is available on the device
     */
    fun isAvailable(): <PERSON><PERSON><PERSON>
    
    /**
     * Check if user has enrolled biometric credentials
     */
    fun isEnrolled(): <PERSON><PERSON><PERSON>
    
    /**
     * Authenticate user with biometrics
     */
    suspend fun authenticate(
        title: String = "Biometric Authentication",
        subtitle: String = "Use your biometric credential to authenticate",
        description: String = "Place your finger on the sensor or look at the camera"
    ): BiometricResult
    
    /**
     * Check if biometric authentication is enabled for the app
     */
    suspend fun isBiometricEnabledForApp(): <PERSON><PERSON>an
    
    /**
     * Enable biometric authentication for the app
     */
    suspend fun enableBiometricForApp()
    
    /**
     * Disable biometric authentication for the app
     */
    suspend fun disableBiometricForApp()
}
