package com.peliplat.core.security

/**
 * Secure storage interface for storing sensitive data like tokens
 * Platform-specific implementations will handle encryption and secure storage
 */
expect class SecureStorage {
    
    /**
     * Store a key-value pair securely
     */
    suspend fun store(key: String, value: String)
    
    /**
     * Retrieve a value by key
     */
    suspend fun get(key: String): String?
    
    /**
     * Remove a key-value pair
     */
    suspend fun remove(key: String)
    
    /**
     * Clear all stored data
     */
    suspend fun clear()
    
    /**
     * Check if a key exists
     */
    suspend fun contains(key: String): <PERSON>olean
}

/**
 * Common secure storage keys
 */
object SecureStorageKeys {
    const val ACCESS_TOKEN = "access_token"
    const val REFRESH_TOKEN = "refresh_token"
    const val TOKEN_EXPIRY = "token_expiry"
    const val USER_ID = "user_id"
    const val USER_EMAIL = "user_email"
    const val BIOMETRIC_ENABLED = "biometric_enabled"
    const val REMEMBER_ME = "remember_me"
}
