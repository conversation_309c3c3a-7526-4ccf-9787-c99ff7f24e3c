package com.peliplat.presentation.theme

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

// Peliplat Design System Colors
object PeliplatColors {
    val Primary = Color(0xFF3F51B5)
    val PrimaryVariant = Color(0xFF303F9F)
    val Secondary = Color(0xFFFF4081)
    val SecondaryVariant = Color(0xFFF50057)
    val Background = Color(0xFF121212)
    val Surface = Color(0xFF1E1E1E)
    val Error = Color(0xFFCF6679)
    val OnPrimary = Color(0xFFFFFFFF)
    val OnSecondary = Color(0xFFFFFFFF)
    val OnBackground = Color(0xFFFFFFFF)
    val OnSurface = Color(0xFFFFFFFF)
    val OnError = Color(0xFF000000)
    val TextDarkBackground = Color(0xFFFFFFFF)
    val TextLightBackground = Color(0xFF000000)
    val IconDarkBackground = Color(0xFFFFFFFF)
    val IconLightBackground = Color(0xFF000000)
}

// Peliplat Design System Typography
object PeliplatTypography {
    val H1 = TextStyle(
        fontSize = 34.sp,
        fontWeight = FontWeight.Bold,
        fontFamily = FontFamily.SansSerif
    )
    val H2 = TextStyle(
        fontSize = 24.sp,
        fontWeight = FontWeight.Bold,
        fontFamily = FontFamily.SansSerif
    )
    val H3 = TextStyle(
        fontSize = 20.sp,
        fontWeight = FontWeight.Bold,
        fontFamily = FontFamily.SansSerif
    )
    val Body1 = TextStyle(
        fontSize = 16.sp,
        fontWeight = FontWeight.Normal,
        fontFamily = FontFamily.SansSerif
    )
    val Body2 = TextStyle(
        fontSize = 14.sp,
        fontWeight = FontWeight.Normal,
        fontFamily = FontFamily.SansSerif
    )
    val Caption = TextStyle(
        fontSize = 12.sp,
        fontWeight = FontWeight.Normal,
        fontFamily = FontFamily.SansSerif
    )
    val Button = TextStyle(
        fontSize = 14.sp,
        fontWeight = FontWeight.Medium,
        fontFamily = FontFamily.SansSerif
    )
}

// Peliplat Design System Spacing
object PeliplatSpacing {
    val ExtraSmall = 4.dp
    val Small = 8.dp
    val Medium = 16.dp
    val Large = 24.dp
    val ExtraLarge = 32.dp
}

// Peliplat Design System Component Styles
object PeliplatComponentStyles {
    val AppBarHeight = 56.dp
    val CardCornerRadius = 8.dp
    val CardElevation = 2.dp
    val ButtonCornerRadius = 4.dp
    val TextFieldCornerRadius = 4.dp
}

// Create Material3 ColorScheme from Peliplat colors
private val PeliplatDarkColorScheme = darkColorScheme(
    primary = PeliplatColors.Primary,
    onPrimary = PeliplatColors.OnPrimary,
    primaryContainer = PeliplatColors.PrimaryVariant,
    onPrimaryContainer = PeliplatColors.OnPrimary,
    secondary = PeliplatColors.Secondary,
    onSecondary = PeliplatColors.OnSecondary,
    secondaryContainer = PeliplatColors.SecondaryVariant,
    onSecondaryContainer = PeliplatColors.OnSecondary,
    tertiary = PeliplatColors.Secondary,
    onTertiary = PeliplatColors.OnSecondary,
    error = PeliplatColors.Error,
    onError = PeliplatColors.OnError,
    errorContainer = PeliplatColors.Error,
    onErrorContainer = PeliplatColors.OnError,
    background = PeliplatColors.Background,
    onBackground = PeliplatColors.OnBackground,
    surface = PeliplatColors.Surface,
    onSurface = PeliplatColors.OnSurface,
    surfaceVariant = PeliplatColors.Surface,
    onSurfaceVariant = PeliplatColors.OnSurface.copy(alpha = 0.7f),
    outline = PeliplatColors.OnSurface.copy(alpha = 0.3f),
    outlineVariant = PeliplatColors.OnSurface.copy(alpha = 0.1f),
    scrim = Color.Black.copy(alpha = 0.5f),
    inverseSurface = PeliplatColors.OnSurface,
    inverseOnSurface = PeliplatColors.Surface,
    inversePrimary = PeliplatColors.Primary
)

// Create Material3 Typography from Peliplat typography
private val PeliplatMaterial3Typography = Typography(
    displayLarge = PeliplatTypography.H1,
    displayMedium = PeliplatTypography.H1.copy(fontSize = 30.sp),
    displaySmall = PeliplatTypography.H2,
    headlineLarge = PeliplatTypography.H2,
    headlineMedium = PeliplatTypography.H3,
    headlineSmall = PeliplatTypography.H3.copy(fontSize = 18.sp),
    titleLarge = PeliplatTypography.H3,
    titleMedium = PeliplatTypography.Body1.copy(fontWeight = FontWeight.Medium),
    titleSmall = PeliplatTypography.Body2.copy(fontWeight = FontWeight.Medium),
    bodyLarge = PeliplatTypography.Body1,
    bodyMedium = PeliplatTypography.Body2,
    bodySmall = PeliplatTypography.Caption,
    labelLarge = PeliplatTypography.Button,
    labelMedium = PeliplatTypography.Button.copy(fontSize = 12.sp),
    labelSmall = PeliplatTypography.Caption.copy(fontSize = 10.sp)
)

@Composable
fun PeliplatTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    content: @Composable () -> Unit
) {
    val colorScheme = PeliplatDarkColorScheme // Always use dark theme for Peliplat
    
    MaterialTheme(
        colorScheme = colorScheme,
        typography = PeliplatMaterial3Typography,
        content = content
    )
}
