package com.peliplat.presentation.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.peliplat.presentation.components.*
import com.peliplat.presentation.viewmodel.ArticleDetailViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ArticleDetailScreen(
    articleId: String,
    viewModel: ArticleDetailViewModel,
    onNavigateBack: () -> Unit,
    onAuthorClick: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    val uiState = viewModel.uiState
    
    LaunchedEffect(articleId) {
        viewModel.loadArticle(articleId)
    }
    
    // Handle error state
    uiState.error?.let { error ->
        LaunchedEffect(error) {
            // Show error snackbar or handle error
            viewModel.clearError()
        }
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Article") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Filled.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when {
                uiState.isLoading -> {
                    LoadingIndicator(
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                
                uiState.article != null -> {
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(bottom = 16.dp)
                    ) {
                        item {
                            ArticleContent(
                                article = uiState.article,
                                onAuthorClick = onAuthorClick,
                                onLikeClick = { viewModel.likeArticle(it) },
                                onBookmarkClick = { viewModel.bookmarkArticle(it) }
                            )
                        }
                        
                        item {
                            Divider(
                                modifier = Modifier.padding(vertical = 16.dp),
                                color = MaterialTheme.colorScheme.outlineVariant
                            )
                        }
                        
                        item {
                            CommentSection(
                                comments = uiState.comments,
                                newCommentText = uiState.newCommentText,
                                isSubmittingComment = uiState.isSubmittingComment,
                                onCommentTextChange = viewModel::updateNewCommentText,
                                onSubmitComment = { viewModel.submitComment(articleId) },
                                onLikeComment = viewModel::likeComment,
                                onAuthorClick = onAuthorClick
                            )
                        }
                    }
                }
                
                uiState.error != null -> {
                    ErrorMessage(
                        message = uiState.error,
                        onRetry = { viewModel.loadArticle(articleId) },
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
            }
        }
    }
}

@Composable
private fun ArticleContent(
    article: com.peliplat.domain.model.Article,
    onAuthorClick: (String) -> Unit,
    onLikeClick: (String) -> Unit,
    onBookmarkClick: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        // Cover image placeholder (if needed)
        article.coverImageUrl?.let { imageUrl ->
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(200.dp),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "📷",
                        style = MaterialTheme.typography.displayMedium
                    )
                }
            }
            Spacer(modifier = Modifier.height(16.dp))
        }
        
        // Title
        Text(
            text = article.title,
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onSurface
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Author info
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            UserAvatar(
                user = article.author,
                size = 48.dp,
                onClick = { onAuthorClick(article.author.id) }
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = article.author.displayName,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = formatTimeAgo(article.publishedAt),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Tags
        if (article.tags.isNotEmpty()) {
            TagRow(
                tags = article.tags,
                modifier = Modifier.fillMaxWidth()
            )
            Spacer(modifier = Modifier.height(16.dp))
        }
        
        // Content
        Text(
            text = article.content,
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurface
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Action buttons
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row {
                ActionButton(
                    icon = if (article.isLiked) "❤️" else "🤍",
                    text = article.likesCount.toString(),
                    onClick = { onLikeClick(article.id) }
                )
                
                Spacer(modifier = Modifier.width(16.dp))
                
                ActionButton(
                    icon = "💬",
                    text = article.commentsCount.toString(),
                    onClick = { /* Scroll to comments */ }
                )
            }
            
            IconButton(
                onClick = { onBookmarkClick(article.id) }
            ) {
                Text(
                    text = if (article.isBookmarked) "🔖" else "📑",
                    style = MaterialTheme.typography.titleMedium
                )
            }
        }
    }
}
