package com.peliplat.presentation.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.peliplat.presentation.components.ArticleCard
import com.peliplat.presentation.components.ErrorMessage
import com.peliplat.presentation.components.LoadingIndicator
import com.peliplat.presentation.theme.PeliplatSpacing
import com.peliplat.presentation.theme.PeliplatComponentStyles
import com.peliplat.presentation.theme.PeliplatTypography
import com.peliplat.presentation.viewmodel.ArticleListViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    articleListViewModel: ArticleListViewModel,
    onArticleClick: (String) -> Unit,
    onAuthorClick: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    val uiState = articleListViewModel.uiState
    val listState = rememberLazyListState()

    // Debug information
    LaunchedEffect(uiState) {
        println("🏠 HomeScreen - Articles count: ${uiState.articles.size}")
        println("🏠 HomeScreen - Loading: ${uiState.isLoading}")
        println("🏠 HomeScreen - Error: ${uiState.error}")
    }

    // Load more articles when reaching the end
    LaunchedEffect(listState) {
        snapshotFlow { listState.layoutInfo.visibleItemsInfo.lastOrNull()?.index }
            .collect { lastVisibleIndex ->
                if (lastVisibleIndex != null &&
                    lastVisibleIndex >= uiState.articles.size - 3 &&
                    uiState.hasMore &&
                    !uiState.isLoading) {
                    articleListViewModel.loadMoreArticles()
                }
            }
    }
    
    Box(
        modifier = modifier.fillMaxSize()
    ) {
        LazyColumn(
            state = listState,
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(vertical = PeliplatSpacing.Small)
        ) {
            // Featured Section
            item {
                SectionHeader("Featured Content")
            }
            item {
                FeaturedContentRow(
                    articles = uiState.articles.take(5),
                    onArticleClick = onArticleClick
                )
            }

            // Latest Articles Section
            item {
                SectionHeader("Latest Articles")
            }
            item {
                Text(
                    text = "Discover the latest movie discussions and reviews",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(horizontal = PeliplatSpacing.Medium)
                )
                Spacer(modifier = Modifier.height(PeliplatSpacing.Small))
            }

            // Articles
            items(uiState.articles) { article ->
                ArticleCard(
                    article = article,
                    onArticleClick = onArticleClick,
                    onAuthorClick = onAuthorClick,
                    onLikeClick = { articleListViewModel.likeArticle(it) },
                    onBookmarkClick = { articleListViewModel.bookmarkArticle(it) }
                )
            }
                
                // Loading indicator for pagination
                if (uiState.isLoading && uiState.articles.isNotEmpty()) {
                    item {
                        LoadingIndicator(
                            modifier = Modifier.padding(16.dp)
                        )
                    }
                }
                
                // Error message
                uiState.error?.let { error ->
                    item {
                        ErrorMessage(
                            message = error,
                            onRetry = { articleListViewModel.clearError() },
                            modifier = Modifier.padding(16.dp)
                        )
                    }
                }
            }
        
        // Initial loading state
        if (uiState.isLoading && uiState.articles.isEmpty()) {
            LoadingIndicator(
                modifier = Modifier.align(Alignment.Center)
            )
        }
        
        // Empty state
        if (!uiState.isLoading && uiState.articles.isEmpty() && uiState.error == null) {
            Column(
                modifier = Modifier
                    .align(Alignment.Center)
                    .padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "📰",
                    style = MaterialTheme.typography.displayMedium
                )
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "No articles yet",
                    style = MaterialTheme.typography.titleLarge
                )
                Text(
                    text = "Loading articles from mock API...",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Spacer(modifier = Modifier.height(16.dp))
                Button(
                    onClick = { articleListViewModel.refresh() }
                ) {
                    Text("Retry Loading")
                }
            }
        }
    }
}

@Composable
fun SectionHeader(title: String) {
    Text(
        text = title,
        style = PeliplatTypography.H3,
        color = MaterialTheme.colorScheme.onBackground,
        fontWeight = FontWeight.Bold,
        modifier = Modifier.padding(
            start = PeliplatSpacing.Medium,
            top = PeliplatSpacing.Medium,
            bottom = PeliplatSpacing.Small
        )
    )
}

@Composable
fun FeaturedContentRow(
    articles: List<com.peliplat.domain.model.Article>,
    onArticleClick: (String) -> Unit
) {
    LazyRow(
        contentPadding = PaddingValues(
            horizontal = PeliplatSpacing.Medium,
            vertical = PeliplatSpacing.Small
        ),
        horizontalArrangement = Arrangement.spacedBy(PeliplatSpacing.Small)
    ) {
        items(articles) { article ->
            FeaturedCard(
                article = article,
                onArticleClick = onArticleClick
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FeaturedCard(
    article: com.peliplat.domain.model.Article,
    onArticleClick: (String) -> Unit
) {
    Card(
        modifier = Modifier
            .width(280.dp)
            .height(180.dp)
            .clip(RoundedCornerShape(PeliplatComponentStyles.CardCornerRadius)),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = PeliplatComponentStyles.CardElevation
        ),
        onClick = { onArticleClick(article.id) }
    ) {
        Column {
            // Article image placeholder
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(120.dp)
                    .clip(RoundedCornerShape(topStart = PeliplatComponentStyles.CardCornerRadius, topEnd = PeliplatComponentStyles.CardCornerRadius)),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "🎬",
                    style = MaterialTheme.typography.displayMedium
                )
            }

            Column(
                modifier = Modifier.padding(PeliplatSpacing.Small)
            ) {
                Text(
                    text = article.title,
                    style = PeliplatTypography.Body1,
                    color = MaterialTheme.colorScheme.onSurface,
                    fontWeight = FontWeight.Medium,
                    maxLines = 2
                )
                Spacer(modifier = Modifier.height(PeliplatSpacing.ExtraSmall))
                Text(
                    text = "by ${article.author.displayName}",
                    style = PeliplatTypography.Caption,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

