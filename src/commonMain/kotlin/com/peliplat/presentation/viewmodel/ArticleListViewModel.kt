package com.peliplat.presentation.viewmodel

import androidx.compose.runtime.*
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.peliplat.data.remote.PaginatedResponse
import com.peliplat.domain.model.Article
import com.peliplat.domain.usecase.*
import kotlinx.coroutines.launch

data class ArticleListUiState(
    val isLoading: Boolean = false,
    val articles: List<Article> = emptyList(),
    val hasMore: Boolean = true,
    val currentPage: Int = 1,
    val error: String? = null,
    val isRefreshing: Boolean = false
)

class ArticleListViewModel(
    private val getArticlesUseCase: GetArticlesUseCase,
    private val likeArticleUseCase: LikeArticleUseCase,
    private val bookmarkArticleUseCase: BookmarkArticleUseCase
) : ViewModel() {
    
    var uiState by mutableStateOf(ArticleListUiState())
        private set
    
    init {
        println("📰 ArticleListViewModel - Initializing and loading articles...")
        loadArticles()
    }
    
    fun loadArticles(refresh: Boolean = false) {
        viewModelScope.launch {
            if (refresh) {
                uiState = uiState.copy(isRefreshing = true, error = null)
            } else if (uiState.currentPage == 1) {
                uiState = uiState.copy(isLoading = true, error = null)
            }
            
            val page = if (refresh) 1 else uiState.currentPage
            
            getArticlesUseCase(page)
                .onSuccess { response ->
                    println("📰 ArticleListViewModel - Success! Got ${response.items.size} articles")
                    val newArticles = if (refresh || page == 1) {
                        response.items
                    } else {
                        uiState.articles + response.items
                    }

                    uiState = uiState.copy(
                        isLoading = false,
                        isRefreshing = false,
                        articles = newArticles,
                        hasMore = response.hasNext,
                        currentPage = if (refresh) 2 else page + 1,
                        error = null
                    )
                    println("📰 ArticleListViewModel - Updated state with ${newArticles.size} total articles")
                }
                .onFailure { exception ->
                    println("📰 ArticleListViewModel - Error: ${exception.message}")
                    uiState = uiState.copy(
                        isLoading = false,
                        isRefreshing = false,
                        error = exception.message
                    )
                }
        }
    }
    
    fun loadMoreArticles() {
        if (!uiState.isLoading && uiState.hasMore) {
            loadArticles()
        }
    }
    
    fun likeArticle(articleId: String) {
        viewModelScope.launch {
            likeArticleUseCase(articleId)
                .onSuccess {
                    // Update the article in the list
                    val updatedArticles = uiState.articles.map { article ->
                        if (article.id == articleId) {
                            article.copy(
                                isLiked = !article.isLiked,
                                likesCount = if (article.isLiked) article.likesCount - 1 else article.likesCount + 1
                            )
                        } else {
                            article
                        }
                    }
                    uiState = uiState.copy(articles = updatedArticles)
                }
        }
    }
    
    fun bookmarkArticle(articleId: String) {
        viewModelScope.launch {
            bookmarkArticleUseCase(articleId)
                .onSuccess {
                    // Update the article in the list
                    val updatedArticles = uiState.articles.map { article ->
                        if (article.id == articleId) {
                            article.copy(isBookmarked = !article.isBookmarked)
                        } else {
                            article
                        }
                    }
                    uiState = uiState.copy(articles = updatedArticles)
                }
        }
    }
    
    fun refresh() {
        loadArticles(refresh = true)
    }
    
    fun clearError() {
        uiState = uiState.copy(error = null)
    }
}

