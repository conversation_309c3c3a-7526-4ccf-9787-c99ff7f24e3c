package com.peliplat.presentation.viewmodel

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.peliplat.domain.model.Article
import com.peliplat.domain.model.Comment
import com.peliplat.domain.usecase.*
import kotlinx.coroutines.launch

data class ArticleDetailUiState(
    val isLoading: Boolean = false,
    val article: Article? = null,
    val comments: List<Comment> = emptyList(),
    val isLoadingComments: Boolean = false,
    val error: String? = null,
    val isSubmittingComment: Boolean = false,
    val newCommentText: String = ""
)

class ArticleDetailViewModel(
    private val getArticleUseCase: GetArticleUseCase,
    private val getArticleCommentsUseCase: GetArticleCommentsUseCase,
    private val createCommentUseCase: CreateCommentUseCase,
    private val likeArticleUseCase: LikeArticleUseCase,
    private val bookmarkArticleUseCase: BookmarkArticleUseCase,
    private val likeCommentUseCase: LikeCommentUseCase
) : ViewModel() {
    
    var uiState by mutableStateOf(ArticleDetailUiState())
        private set
    
    fun loadArticle(articleId: String) {
        viewModelScope.launch {
            uiState = uiState.copy(isLoading = true, error = null)
            
            getArticleUseCase(articleId)
                .onSuccess { article ->
                    uiState = uiState.copy(
                        isLoading = false,
                        article = article,
                        error = null
                    )
                    // Load comments after article is loaded
                    loadComments(articleId)
                }
                .onFailure { exception ->
                    uiState = uiState.copy(
                        isLoading = false,
                        error = exception.message
                    )
                }
        }
    }
    
    private fun loadComments(articleId: String) {
        viewModelScope.launch {
            uiState = uiState.copy(isLoadingComments = true)
            
            getArticleCommentsUseCase(articleId)
                .onSuccess { comments ->
                    uiState = uiState.copy(
                        isLoadingComments = false,
                        comments = comments
                    )
                }
                .onFailure { exception ->
                    uiState = uiState.copy(
                        isLoadingComments = false,
                        error = exception.message
                    )
                }
        }
    }
    
    fun updateNewCommentText(text: String) {
        uiState = uiState.copy(newCommentText = text)
    }
    
    fun submitComment(articleId: String, parentId: String? = null) {
        if (uiState.newCommentText.isBlank()) return
        
        viewModelScope.launch {
            uiState = uiState.copy(isSubmittingComment = true)
            
            createCommentUseCase(articleId, uiState.newCommentText, parentId)
                .onSuccess { newComment ->
                    uiState = uiState.copy(
                        isSubmittingComment = false,
                        newCommentText = "",
                        comments = uiState.comments + newComment
                    )
                    // Update article comment count
                    uiState.article?.let { article ->
                        uiState = uiState.copy(
                            article = article.copy(commentsCount = article.commentsCount + 1)
                        )
                    }
                }
                .onFailure { exception ->
                    uiState = uiState.copy(
                        isSubmittingComment = false,
                        error = exception.message
                    )
                }
        }
    }
    
    fun likeArticle(articleId: String) {
        viewModelScope.launch {
            likeArticleUseCase(articleId)
                .onSuccess {
                    uiState.article?.let { article ->
                        uiState = uiState.copy(
                            article = article.copy(
                                isLiked = !article.isLiked,
                                likesCount = if (article.isLiked) article.likesCount - 1 else article.likesCount + 1
                            )
                        )
                    }
                }
        }
    }
    
    fun bookmarkArticle(articleId: String) {
        viewModelScope.launch {
            bookmarkArticleUseCase(articleId)
                .onSuccess {
                    uiState.article?.let { article ->
                        uiState = uiState.copy(
                            article = article.copy(isBookmarked = !article.isBookmarked)
                        )
                    }
                }
        }
    }
    
    fun likeComment(commentId: String) {
        viewModelScope.launch {
            likeCommentUseCase(commentId)
                .onSuccess {
                    val updatedComments = uiState.comments.map { comment ->
                        if (comment.id == commentId) {
                            comment.copy(
                                isLiked = !comment.isLiked,
                                likesCount = if (comment.isLiked) comment.likesCount - 1 else comment.likesCount + 1
                            )
                        } else {
                            comment
                        }
                    }
                    uiState = uiState.copy(comments = updatedComments)
                }
        }
    }
    
    fun clearError() {
        uiState = uiState.copy(error = null)
    }
}
