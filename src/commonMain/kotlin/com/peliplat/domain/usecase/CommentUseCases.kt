package com.peliplat.domain.usecase

import com.peliplat.domain.model.Comment
import com.peliplat.domain.repository.ArticleRepository

class GetArticleCommentsUseCase(
    private val articleRepository: ArticleRepository
) {
    suspend operator fun invoke(articleId: String): Result<List<Comment>> {
        if (articleId.isBlank()) {
            return Result.failure(Exception("Article ID cannot be empty"))
        }
        return articleRepository.getArticleComments(articleId)
    }
}

class CreateCommentUseCase(
    private val articleRepository: ArticleRepository
) {
    suspend operator fun invoke(
        articleId: String,
        content: String,
        parentId: String? = null
    ): Result<Comment> {
        if (articleId.isBlank()) {
            return Result.failure(Exception("Article ID cannot be empty"))
        }
        if (content.isBlank()) {
            return Result.failure(Exception("Comment content cannot be empty"))
        }
        return articleRepository.createComment(articleId, content, parentId)
    }
}

class LikeCommentUseCase(
    private val articleRepository: ArticleRepository
) {
    suspend operator fun invoke(commentId: String): Result<Boolean> {
        if (commentId.isBlank()) {
            return Result.failure(Exception("Comment ID cannot be empty"))
        }
        return articleRepository.likeComment(commentId)
    }
}
