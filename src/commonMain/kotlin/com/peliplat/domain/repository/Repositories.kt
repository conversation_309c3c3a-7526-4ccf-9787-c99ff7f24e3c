package com.peliplat.domain.repository

import com.peliplat.data.remote.ApiResponse
import com.peliplat.data.remote.PaginatedResponse
import com.peliplat.domain.model.*

interface AuthRepository {
    suspend fun login(email: String, password: String): Result<User>
    suspend fun register(username: String, email: String, password: String, displayName: String): Result<User>
    suspend fun logout()
    suspend fun getCurrentUser(): Result<User?>
    suspend fun isLoggedIn(): Boolean
}

interface ArticleRepository {
    suspend fun getArticles(page: Int = 1, pageSize: Int = 20): Result<PaginatedResponse<Article>>
    suspend fun getArticle(id: String): Result<Article>
    suspend fun createArticle(title: String, content: String, excerpt: String, coverImageUrl: String?, tags: List<String>): Result<Article>
    suspend fun likeArticle(id: String): Result<Boolean>
    suspend fun bookmarkArticle(id: String): Result<Boolean>
    suspend fun searchArticles(query: String): Result<List<Article>>
    suspend fun getArticleComments(articleId: String): Result<List<Comment>>
    suspend fun createComment(articleId: String, content: String, parentId: String? = null): Result<Comment>
    suspend fun likeComment(commentId: String): Result<Boolean>
}

interface DiscussionRepository {
    suspend fun getDiscussions(page: Int = 1, pageSize: Int = 20): Result<PaginatedResponse<Discussion>>
    suspend fun getDiscussion(id: String): Result<Discussion>
    suspend fun createDiscussion(title: String, content: String, tags: List<String>): Result<Discussion>
    suspend fun likeDiscussion(id: String): Result<Boolean>
}

interface UserRepository {
    suspend fun getUser(id: String): Result<User>
    suspend fun followUser(id: String): Result<Boolean>
    suspend fun unfollowUser(id: String): Result<Boolean>
    suspend fun getUserArticles(userId: String, page: Int = 1): Result<PaginatedResponse<Article>>
}

interface SearchRepository {
    suspend fun searchContent(query: String, type: String = "all"): Result<List<Any>>
    suspend fun getPopularTags(): Result<List<Tag>>
    suspend fun searchByTag(tag: String): Result<List<Any>>
}

