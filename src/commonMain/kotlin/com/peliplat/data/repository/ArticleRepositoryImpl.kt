package com.peliplat.data.repository

import com.peliplat.data.remote.ApiClient
import com.peliplat.data.remote.CreateArticleRequest
import com.peliplat.data.remote.PaginatedResponse
import com.peliplat.domain.model.Article
import com.peliplat.domain.model.Comment
import com.peliplat.domain.repository.ArticleRepository

class ArticleRepositoryImpl(
    private val apiClient: ApiClient
) : ArticleRepository {
    
    override suspend fun getArticles(page: Int, pageSize: Int): Result<PaginatedResponse<Article>> {
        return try {
            val response = apiClient.getArticles(page, pageSize)
            if (response.success && response.data != null) {
                Result.success(response.data)
            } else {
                Result.failure(Exception(response.message ?: "Failed to fetch articles"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun getArticle(id: String): Result<Article> {
        return try {
            val response = apiClient.getArticle(id)
            if (response.success && response.data != null) {
                Result.success(response.data)
            } else {
                Result.failure(Exception(response.message ?: "Article not found"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun createArticle(
        title: String,
        content: String,
        excerpt: String,
        coverImageUrl: String?,
        tags: List<String>
    ): Result<Article> {
        return try {
            val response = apiClient.createArticle(
                CreateArticleRequest(title, content, excerpt, coverImageUrl, tags)
            )
            if (response.success && response.data != null) {
                Result.success(response.data)
            } else {
                Result.failure(Exception(response.message ?: "Failed to create article"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun likeArticle(id: String): Result<Boolean> {
        // Mock implementation
        return Result.success(true)
    }
    
    override suspend fun bookmarkArticle(id: String): Result<Boolean> {
        // Mock implementation
        return Result.success(true)
    }
    
    override suspend fun searchArticles(query: String): Result<List<Article>> {
        return try {
            val response = apiClient.searchContent(query, "articles")
            if (response.success && response.data != null) {
                val articles = response.data.filterIsInstance<Article>()
                Result.success(articles)
            } else {
                Result.failure(Exception(response.message ?: "Search failed"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getArticleComments(articleId: String): Result<List<Comment>> {
        return try {
            val response = apiClient.getArticleComments(articleId)
            if (response.success && response.data != null) {
                Result.success(response.data)
            } else {
                Result.failure(Exception(response.message ?: "Failed to fetch comments"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun createComment(articleId: String, content: String, parentId: String?): Result<Comment> {
        return try {
            val response = apiClient.createComment(articleId, content, parentId)
            if (response.success && response.data != null) {
                Result.success(response.data)
            } else {
                Result.failure(Exception(response.message ?: "Failed to create comment"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun likeComment(commentId: String): Result<Boolean> {
        // Mock implementation
        return Result.success(true)
    }
}

