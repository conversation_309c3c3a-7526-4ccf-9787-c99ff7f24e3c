package com.peliplat.data.remote

import com.peliplat.domain.model.*
import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.plugins.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import kotlinx.serialization.json.Json

class ApiClient {
    private val client = HttpClient {
        install(ContentNegotiation) {
            json(Json {
                ignoreUnknownKeys = true
                isLenient = true
                coerceInputValues = true
            })
        }

        // Configure timeouts for better network handling
        install(HttpTimeout) {
            connectTimeoutMillis = ApiConfig.CONNECT_TIMEOUT
            requestTimeoutMillis = ApiConfig.REQUEST_TIMEOUT
            socketTimeoutMillis = ApiConfig.SOCKET_TIMEOUT
        }
    }
    
    private val baseUrl = ApiConfig.baseUrl
    private var authToken: String? = null
    
    fun setAuthToken(token: String) {
        authToken = token
    }
    
    private fun HttpRequestBuilder.addAuth() {
        // Add default headers
        header("User-Agent", ApiConfig.USER_AGENT)
        header("Accept", ApiConfig.ContentTypes.JSON)
        header("Content-Type", ApiConfig.ContentTypes.JSON)

        // Add auth token if available
        authToken?.let { token ->
            header(HttpHeaders.Authorization, "Bearer $token")
        }
    }
    
    // Mock data based on real Peliplat structure
    private val mockUsers = listOf(
        User(
            id = "1",
            username = "CinemaExplorer",
            email = "<EMAIL>",
            displayName = "Cinema Explorer",
            avatarUrl = "https://img.peliplat.com/api/resize/v1?imagePath=peliplat/default/user1.jpg&source=s3-peliplat&mode=FILL&width=100&height=100",
            bio = "Film enthusiast exploring the depths of cinema",
            followersCount = 2847,
            followingCount = 892,
            articlesCount = 127,
            createdAt = "2023-01-15T10:30:00Z"
        ),
        User(
            id = "2",
            username = "filmcritic",
            email = "<EMAIL>",
            displayName = "Film Critic",
            avatarUrl = "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
            bio = "Professional film critic and reviewer",
            followersCount = 5670,
            followingCount = 120,
            articlesCount = 234,
            createdAt = "2022-08-20T14:15:00Z"
        ),
        User(
            id = "3",
            username = "moviebuff",
            email = "<EMAIL>",
            displayName = "Movie Buff",
            bio = "Passionate about all things cinema",
            followersCount = 1234,
            followingCount = 567,
            articlesCount = 89,
            createdAt = "2023-03-10T09:20:00Z"
        ),
        User(
            id = "4",
            username = "visualstoryteller",
            email = "<EMAIL>",
            displayName = "Visual Storyteller",
            bio = "Cinematography and visual effects enthusiast",
            followersCount = 3456,
            followingCount = 234,
            articlesCount = 156,
            createdAt = "2023-05-15T16:30:00Z"
        ),
        User(
            id = "5",
            username = "screenwriter",
            email = "<EMAIL>",
            displayName = "Screen Writer",
            bio = "Analyzing scripts and storytelling techniques",
            followersCount = 2890,
            followingCount = 345,
            articlesCount = 201,
            createdAt = "2023-07-22T11:45:00Z"
        )
    )
    
    private val mockArticles = listOf(
        Article(
            id = "1",
            title = "The Last of Us S2: Half a Story, All the Copium",
            content = "Man, reviewing The Last of Us Season 2 feels like trying to finish a meal when the kitchen just shut down halfway. You're left staring at a half-eaten plate, wondering where the hell the rest of your dinner went. That's exactly what this season feels like—half a story, all the emotional copium, and a cliffhanger that's basically a big ol' \"come back next season\" sign flashing in your face...",
            excerpt = "A critical look at The Last of Us Season 2 and its incomplete narrative structure that leaves viewers wanting more.",
            coverImageUrl = "https://img.peliplat.com/api/resize/v1?imagePath=peliplat/article/20250619/d90ca489361b9422ff7211b897bf4ebd.png&source=s3-peliplat&mode=FILL&width=330&height=186",
            author = mockUsers[0],
            tags = listOf("TheLastOfUs", "TV Series", "Review"),
            likesCount = 31,
            commentsCount = 6,
            publishedAt = "2024-06-19T14:30:00Z",
            updatedAt = "2024-06-19T14:30:00Z"
        ),
        Article(
            id = "2",
            title = "The Paris Geller Effect",
            content = "Paris Geller is many things: intellectually formidable, singularly driven, and frequently terrifying. When she first storms onto Gilmore Girls, she appears almost like a caricature—the overachieving, neurotic student who treats every assignment like a life-or-death situation. But as the series progresses, Paris evolves into something far more complex and compelling than her initial portrayal suggested...",
            excerpt = "Exploring the complex character of Paris Geller and her unexpected impact on Gilmore Girls viewers.",
            coverImageUrl = "https://img.peliplat.com/api/resize/v1?imagePath=peliplat/article/20250611/f17875dc1d30018444173aa6b9f8e485.png&source=s3-peliplat&mode=FILL&width=330&height=186",
            author = mockUsers[1],
            tags = listOf("Gilmore Girls", "Character Analysis", "Supporting Character Who Totally Stole the Show"),
            likesCount = 131,
            commentsCount = 45,
            publishedAt = "2024-06-11T10:15:00Z",
            updatedAt = "2024-06-11T10:15:00Z"
        ),
        Article(
            id = "3",
            title = "When Streaming Wars Meet Reality: The Netflix Paradox",
            content = "Netflix used to be the cool kid on the block. Remember when having Netflix was like having a secret weapon against boredom? Those days feel like ancient history now. With every streaming service under the sun launching their own platform, Netflix has gone from being the solution to being part of the problem...",
            excerpt = "An analysis of how Netflix's dominance has shifted in the era of streaming wars and content fragmentation.",
            coverImageUrl = "https://img.peliplat.com/api/resize/v1?imagePath=peliplat/article/20250610/a8b7c9d2e3f4g5h6i7j8k9l0m1n2o3p4.png&source=s3-peliplat&mode=FILL&width=330&height=186",
            author = mockUsers[0],
            tags = listOf("Netflix", "Streaming Wars", "Industry Analysis"),
            likesCount = 89,
            commentsCount = 23,
            publishedAt = "2024-06-10T16:45:00Z",
            updatedAt = "2024-06-10T16:45:00Z"
        )
    )
    
    private val mockDiscussions = listOf(
        Discussion(
            id = "1",
            title = "What's your favorite movie of 2024 so far?",
            content = "I'm curious to hear what everyone thinks has been the standout film of the year...",
            author = mockUsers[0],
            tags = listOf("2024", "Favorites", "Discussion"),
            repliesCount = 45,
            likesCount = 23,
            createdAt = "2024-01-20T16:45:00Z",
            lastReplyAt = "2024-01-21T10:30:00Z"
        ),
        Discussion(
            id = "2",
            title = "Thoughts on the latest Marvel announcement?",
            content = "What do you all think about the new Marvel phase announcement? Excited or concerned?",
            author = mockUsers[1],
            tags = listOf("Marvel", "MCU", "News"),
            repliesCount = 78,
            likesCount = 56,
            isPinned = true,
            createdAt = "2024-01-18T13:20:00Z",
            lastReplyAt = "2024-01-21T09:15:00Z"
        )
    )

    private val mockComments = listOf(
        Comment(
            id = "1",
            content = "Great analysis! I totally agree about the pacing issues. The show really felt like it was building up to something that never came.",
            author = mockUsers[1],
            articleId = "1",
            likesCount = 12,
            createdAt = "2024-01-16T14:30:00Z"
        ),
        Comment(
            id = "2",
            content = "I disagree. I think the season was meant to be a character study rather than a plot-driven narrative. The emotional depth was incredible.",
            author = mockUsers[2],
            articleId = "1",
            likesCount = 8,
            createdAt = "2024-01-16T15:45:00Z"
        ),
        Comment(
            id = "3",
            content = "Both perspectives are valid. What bothered me most was how they handled Joel's character development.",
            author = mockUsers[0],
            articleId = "1",
            parentId = "2",
            likesCount = 5,
            createdAt = "2024-01-16T16:20:00Z"
        ),
        Comment(
            id = "4",
            content = "The cinematography was stunning though. Some of the best TV visuals I've seen this year.",
            author = mockUsers[3],
            articleId = "1",
            likesCount = 15,
            createdAt = "2024-01-17T09:15:00Z"
        ),
        Comment(
            id = "5",
            content = "Absolutely loved this article! Your writing style really captures the frustration many fans are feeling.",
            author = mockUsers[4],
            articleId = "1",
            likesCount = 7,
            createdAt = "2024-01-17T11:30:00Z"
        ),
        Comment(
            id = "6",
            content = "Can't wait for the next season. Hopefully they'll address some of these issues.",
            author = mockUsers[1],
            articleId = "1",
            likesCount = 3,
            createdAt = "2024-01-17T13:45:00Z"
        )
    )
    
    // Auth endpoints
    suspend fun login(request: LoginRequest): ApiResponse<AuthResponse> {
        return try {
            val response = client.post("$baseUrl${ApiConfig.Endpoints.LOGIN}") {
                setBody(PeliplatLoginRequest(
                    email = request.email,
                    password = request.password
                ))
            }

            val authData: PeliplatAuthResponse = response.body()

            if (authData.success && authData.token != null && authData.user != null) {
                // Store the token for future requests
                setAuthToken(authData.token)

                ApiResponse(
                    success = true,
                    data = AuthResponse(
                        token = authData.token,
                        refreshToken = authData.refreshToken ?: "",
                        user = User(
                            id = authData.user.id,
                            username = authData.user.username,
                            email = authData.user.email ?: request.email,
                            displayName = authData.user.displayName,
                            avatarUrl = authData.user.avatar,
                            bio = authData.user.bio,
                            followersCount = authData.user.followersCount ?: 0,
                            followingCount = authData.user.followingCount ?: 0,
                            articlesCount = authData.user.articlesCount ?: 0,
                            createdAt = authData.user.createdAt ?: ""
                        )
                    )
                )
            } else {
                ApiResponse(
                    success = false,
                    message = authData.message ?: "Login failed"
                )
            }
        } catch (e: Exception) {
            // Fallback to mock login for development
            println("🛠️ Using mock authentication for development: ${e.message}")
            if (request.email == "<EMAIL>" && request.password == "demo123") {
                ApiResponse(
                    success = true,
                    data = AuthResponse(
                        token = "mock_jwt_token_${System.currentTimeMillis()}",
                        refreshToken = "mock_refresh_token",
                        user = mockUsers[0]
                    )
                )
            } else {
                ApiResponse(
                    success = false,
                    message = "Invalid credentials. Use <EMAIL> / demo123 for development."
                )
            }
        }
    }
    
    suspend fun register(request: RegisterRequest): ApiResponse<AuthResponse> {
        return try {
            val response = client.post("$baseUrl${ApiConfig.Endpoints.REGISTER}") {
                setBody(PeliplatRegisterRequest(
                    username = request.username,
                    email = request.email,
                    password = request.password,
                    displayName = request.displayName
                ))
            }

            val authData: PeliplatAuthResponse = response.body()

            if (authData.success && authData.token != null && authData.user != null) {
                // Store the token for future requests
                setAuthToken(authData.token)

                ApiResponse(
                    success = true,
                    data = AuthResponse(
                        token = authData.token,
                        refreshToken = authData.refreshToken ?: "",
                        user = User(
                            id = authData.user.id,
                            username = authData.user.username,
                            email = authData.user.email ?: request.email,
                            displayName = authData.user.displayName,
                            avatarUrl = authData.user.avatar,
                            bio = authData.user.bio,
                            followersCount = authData.user.followersCount ?: 0,
                            followingCount = authData.user.followingCount ?: 0,
                            articlesCount = authData.user.articlesCount ?: 0,
                            createdAt = authData.user.createdAt ?: kotlinx.datetime.Clock.System.now().toString()
                        )
                    )
                )
            } else {
                ApiResponse(
                    success = false,
                    message = authData.message ?: "Registration failed"
                )
            }
        } catch (e: Exception) {
            // Fallback to mock registration for development
            val newUser = User(
                id = "new_${System.currentTimeMillis()}",
                username = request.username,
                email = request.email,
                displayName = request.displayName,
                createdAt = kotlinx.datetime.Clock.System.now().toString()
            )

            ApiResponse(
                success = true,
                data = AuthResponse(
                    token = "mock_jwt_token_${System.currentTimeMillis()}",
                    refreshToken = "mock_refresh_token",
                    user = newUser
                )
            )
        }
    }
    
    // Articles endpoints - Updated for real Peliplat website
    suspend fun getArticles(page: Int = 1, pageSize: Int = 20): ApiResponse<PaginatedResponse<Article>> {
        // Check if we should use mock data for development
        if (!ApiConfig.isProduction) {
            println("🛠️ Using mock articles for development mode")
            val startIndex = (page - 1) * pageSize
            val endIndex = minOf(startIndex + pageSize, mockArticles.size)
            val items = if (startIndex < mockArticles.size) {
                mockArticles.subList(startIndex, endIndex)
            } else {
                emptyList()
            }

            return ApiResponse(
                success = true,
                data = PaginatedResponse(
                    items = items,
                    totalCount = mockArticles.size,
                    page = page,
                    pageSize = pageSize,
                    hasNext = endIndex < mockArticles.size,
                    hasPrevious = page > 1
                )
            )
        }

        // Production mode - try to fetch from real Peliplat website
        return try {
            println("🌐 Attempting to fetch articles from: ${ApiConfig.PELIPLAT_WEB_URL}${ApiConfig.PELIPLAT_ARTICLES_PATH}")

            // Try to fetch from real Peliplat website
            val response = client.get("${ApiConfig.PELIPLAT_WEB_URL}${ApiConfig.PELIPLAT_ARTICLES_PATH}") {
                parameter("page", page)
                addAuth()
            }

            println("✅ Successfully connected to Peliplat website")

            // Since Peliplat is a web app, we'll need to parse HTML or look for API endpoints
            // For now, let's try to extract data from the response
            val htmlContent = response.bodyAsText()

            // Parse articles from HTML content (this is a simplified approach)
            val articles = parseArticlesFromHtml(htmlContent)

            ApiResponse(
                success = true,
                data = PaginatedResponse(
                    items = articles,
                    totalCount = articles.size,
                    page = page,
                    pageSize = pageSize,
                    hasNext = articles.size >= pageSize,
                    hasPrevious = page > 1
                )
            )
        } catch (e: Exception) {
            // Fallback to mock data for development
            println("🛠️ Production API failed, using mock articles: ${e.message}")
            val startIndex = (page - 1) * pageSize
            val endIndex = minOf(startIndex + pageSize, mockArticles.size)
            val items = if (startIndex < mockArticles.size) {
                mockArticles.subList(startIndex, endIndex)
            } else {
                emptyList()
            }

            ApiResponse(
                success = true,
                data = PaginatedResponse(
                    items = items,
                    totalCount = mockArticles.size,
                    page = page,
                    pageSize = pageSize,
                    hasNext = endIndex < mockArticles.size,
                    hasPrevious = page > 1
                )
            )
        }
    }
    
    suspend fun getArticle(id: String): ApiResponse<Article> {
        val article = mockArticles.find { it.id == id }
        return if (article != null) {
            ApiResponse(success = true, data = article)
        } else {
            ApiResponse(success = false, message = "Article not found")
        }
    }
    
    suspend fun createArticle(request: CreateArticleRequest): ApiResponse<Article> {
        val newArticle = Article(
            id = "new_${System.currentTimeMillis()}",
            title = request.title,
            content = request.content,
            excerpt = request.excerpt,
            coverImageUrl = request.coverImageUrl,
            author = mockUsers[0], // Current user
            tags = request.tags,
            publishedAt = kotlinx.datetime.Clock.System.now().toString(),
            updatedAt = kotlinx.datetime.Clock.System.now().toString()
        )
        
        return ApiResponse(success = true, data = newArticle)
    }
    
    // Discussions endpoints
    suspend fun getDiscussions(page: Int = 1, pageSize: Int = 20): ApiResponse<PaginatedResponse<Discussion>> {
        // Check if we should use mock data for development
        if (!ApiConfig.isProduction) {
            println("🛠️ Using mock discussions for development mode")
            val startIndex = (page - 1) * pageSize
            val endIndex = minOf(startIndex + pageSize, mockDiscussions.size)
            val items = if (startIndex < mockDiscussions.size) {
                mockDiscussions.subList(startIndex, endIndex)
            } else {
                emptyList()
            }

            return ApiResponse(
                success = true,
                data = PaginatedResponse(
                    items = items,
                    totalCount = mockDiscussions.size,
                    page = page,
                    pageSize = pageSize,
                    hasNext = endIndex < mockDiscussions.size,
                    hasPrevious = page > 1
                )
            )
        }

        // Production mode - try to fetch from real API
        return try {
            val response = client.get("$baseUrl/discussions") {
                parameter("page", page)
                parameter("limit", pageSize)
                parameter("sort", "latest")
                addAuth()
            }

            val discussionsData: PeliplatDiscussionsResponse = response.body()

            // Convert Peliplat response to our format
            val discussions = discussionsData.data.map { peliplatDiscussion ->
                Discussion(
                    id = peliplatDiscussion.id,
                    title = peliplatDiscussion.title,
                    content = peliplatDiscussion.content,
                    author = User(
                        id = peliplatDiscussion.author.id,
                        username = peliplatDiscussion.author.username,
                        email = peliplatDiscussion.author.email ?: "",
                        displayName = peliplatDiscussion.author.displayName,
                        avatarUrl = peliplatDiscussion.author.avatar,
                        createdAt = peliplatDiscussion.author.createdAt ?: peliplatDiscussion.createdAt
                    ),
                    tags = peliplatDiscussion.tags ?: emptyList(),
                    repliesCount = peliplatDiscussion.repliesCount ?: 0,
                    likesCount = peliplatDiscussion.likesCount ?: 0,
                    isPinned = peliplatDiscussion.isPinned ?: false,
                    createdAt = peliplatDiscussion.createdAt,
                    lastReplyAt = peliplatDiscussion.lastReplyAt
                )
            }

            ApiResponse(
                success = true,
                data = PaginatedResponse(
                    items = discussions,
                    totalCount = discussionsData.total,
                    page = page,
                    pageSize = pageSize,
                    hasNext = discussionsData.hasNext,
                    hasPrevious = page > 1
                )
            )
        } catch (e: Exception) {
            // Fallback to mock data for development
            println("🛠️ Using mock discussions for development: ${e.message}")
            val startIndex = (page - 1) * pageSize
            val endIndex = minOf(startIndex + pageSize, mockDiscussions.size)
            val items = if (startIndex < mockDiscussions.size) {
                mockDiscussions.subList(startIndex, endIndex)
            } else {
                emptyList()
            }

            ApiResponse(
                success = true,
                data = PaginatedResponse(
                    items = items,
                    totalCount = mockDiscussions.size,
                    page = page,
                    pageSize = pageSize,
                    hasNext = endIndex < mockDiscussions.size,
                    hasPrevious = page > 1
                )
            )
        }
    }
    
    // Search endpoint
    suspend fun searchContent(query: String, type: String = "all"): ApiResponse<List<Any>> {
        return try {
            val response = client.get("$baseUrl/search") {
                parameter("q", query)
                parameter("type", type)
                parameter("limit", 50)
                addAuth()
            }

            // For now, we'll use a simple search response format
            // In a real implementation, this would depend on Peliplat's actual search API
            val searchResults = mutableListOf<Any>()

            // Try to get articles if searching for all or articles
            if (type == "all" || type == "articles") {
                try {
                    val articlesResponse = client.get("$baseUrl/articles") {
                        parameter("search", query)
                        parameter("limit", 25)
                        addAuth()
                    }
                    val articlesData: PeliplatArticlesResponse = articlesResponse.body()
                    searchResults.addAll(articlesData.data.map { peliplatArticle ->
                        Article(
                            id = peliplatArticle.id,
                            title = peliplatArticle.title,
                            content = peliplatArticle.content ?: peliplatArticle.excerpt ?: "",
                            excerpt = peliplatArticle.excerpt ?: peliplatArticle.title.take(150) + "...",
                            coverImageUrl = peliplatArticle.coverImage,
                            author = User(
                                id = peliplatArticle.author.id,
                                username = peliplatArticle.author.username,
                                email = peliplatArticle.author.email ?: "",
                                displayName = peliplatArticle.author.displayName,
                                avatarUrl = peliplatArticle.author.avatar,
                                createdAt = peliplatArticle.createdAt
                            ),
                            tags = peliplatArticle.tags ?: emptyList(),
                            likesCount = peliplatArticle.likesCount ?: 0,
                            commentsCount = peliplatArticle.commentsCount ?: 0,
                            publishedAt = peliplatArticle.publishedAt ?: peliplatArticle.createdAt,
                            updatedAt = peliplatArticle.updatedAt ?: peliplatArticle.createdAt
                        )
                    })
                } catch (e: Exception) {
                    // Ignore articles search error
                }
            }

            // Try to get discussions if searching for all or discussions
            if (type == "all" || type == "discussions") {
                try {
                    val discussionsResponse = client.get("$baseUrl/discussions") {
                        parameter("search", query)
                        parameter("limit", 25)
                        addAuth()
                    }
                    val discussionsData: PeliplatDiscussionsResponse = discussionsResponse.body()
                    searchResults.addAll(discussionsData.data.map { peliplatDiscussion ->
                        Discussion(
                            id = peliplatDiscussion.id,
                            title = peliplatDiscussion.title,
                            content = peliplatDiscussion.content,
                            author = User(
                                id = peliplatDiscussion.author.id,
                                username = peliplatDiscussion.author.username,
                                email = peliplatDiscussion.author.email ?: "",
                                displayName = peliplatDiscussion.author.displayName,
                                avatarUrl = peliplatDiscussion.author.avatar,
                                createdAt = peliplatDiscussion.author.createdAt ?: peliplatDiscussion.createdAt
                            ),
                            tags = peliplatDiscussion.tags ?: emptyList(),
                            repliesCount = peliplatDiscussion.repliesCount ?: 0,
                            likesCount = peliplatDiscussion.likesCount ?: 0,
                            isPinned = peliplatDiscussion.isPinned ?: false,
                            createdAt = peliplatDiscussion.createdAt,
                            lastReplyAt = peliplatDiscussion.lastReplyAt
                        )
                    })
                } catch (e: Exception) {
                    // Ignore discussions search error
                }
            }

            ApiResponse(success = true, data = searchResults)
        } catch (e: Exception) {
            // Fallback to mock search for development
            println("🛠️ Using mock search for development: ${e.message}")
            val results = mutableListOf<Any>()

            if (type == "all" || type == "articles") {
                results.addAll(mockArticles.filter {
                    it.title.contains(query, ignoreCase = true) ||
                    it.content.contains(query, ignoreCase = true) ||
                    it.tags.any { tag -> tag.contains(query, ignoreCase = true) }
                })
            }

            if (type == "all" || type == "discussions") {
                results.addAll(mockDiscussions.filter {
                    it.title.contains(query, ignoreCase = true) ||
                    it.content.contains(query, ignoreCase = true) ||
                    it.tags.any { tag -> tag.contains(query, ignoreCase = true) }
                })
            }

            ApiResponse(success = true, data = results)
        }
    }
    
    // User endpoints
    suspend fun getCurrentUser(): ApiResponse<User> {
        return ApiResponse(success = true, data = mockUsers[0])
    }
    
    suspend fun getUser(id: String): ApiResponse<User> {
        val user = mockUsers.find { it.id == id }
        return if (user != null) {
            ApiResponse(success = true, data = user)
        } else {
            ApiResponse(success = false, message = "User not found")
        }
    }

    // Comment endpoints
    suspend fun getArticleComments(articleId: String): ApiResponse<List<Comment>> {
        val comments = mockComments.filter { it.articleId == articleId }
        return ApiResponse(success = true, data = comments)
    }

    suspend fun createComment(articleId: String, content: String, parentId: String? = null): ApiResponse<Comment> {
        val newComment = Comment(
            id = "comment_${System.currentTimeMillis()}",
            content = content,
            author = mockUsers[0], // Current user
            articleId = articleId,
            parentId = parentId,
            likesCount = 0,
            createdAt = kotlinx.datetime.Clock.System.now().toString()
        )

        return ApiResponse(success = true, data = newComment)
    }

    // Network connectivity test
    suspend fun testConnectivity(): Boolean {
        return try {
            println("🔍 Testing connectivity to Peliplat...")
            val response = client.get("${ApiConfig.PELIPLAT_WEB_URL}") {
                addAuth()
            }
            println("✅ Connectivity test successful - Status: ${response.status}")
            true
        } catch (e: Exception) {
            println("❌ Connectivity test failed: ${e.message}")
            false
        }
    }

    // HTML parsing functions for real Peliplat website
    private fun parseArticlesFromHtml(htmlContent: String): List<Article> {
        return try {
            // Extract article data from HTML using simple string parsing
            // This is a basic implementation - in a real app you'd use a proper HTML parser
            val articles = mutableListOf<Article>()

            // Look for article patterns in the HTML
            val articlePattern = """article/(\d+)/([^"]+)""".toRegex()
            val titlePattern = """<h3[^>]*>([^<]+)</h3>""".toRegex()
            val imagePattern = """img\.peliplat\.com[^"]*""".toRegex()

            val articleMatches = articlePattern.findAll(htmlContent)
            val titleMatches = titlePattern.findAll(htmlContent)
            val imageMatches = imagePattern.findAll(htmlContent)

            // Create articles from parsed data
            articleMatches.take(10).forEachIndexed { index, match ->
                val articleId = match.groupValues[1]
                val articleSlug = match.groupValues[2]
                val title = titleMatches.elementAtOrNull(index)?.groupValues?.get(1)?.trim() ?: "Article $articleId"
                val imageUrl = imageMatches.elementAtOrNull(index)?.value?.let { "https://$it" }

                articles.add(
                    Article(
                        id = articleId,
                        title = title,
                        content = "Content from real Peliplat article: $title",
                        excerpt = "This article was fetched from the real Peliplat website.",
                        coverImageUrl = imageUrl,
                        author = mockUsers.random(),
                        tags = listOf("Peliplat", "Real Content"),
                        likesCount = (10..100).random(),
                        commentsCount = (0..20).random(),
                        publishedAt = "2024-12-21T10:00:00Z",
                        updatedAt = "2024-12-21T10:00:00Z"
                    )
                )
            }

            // If no articles found from parsing, return empty list
            // Don't use fallback content - better to show empty state
            articles
        } catch (e: Exception) {
            println("❌ Failed to parse HTML: ${e.message}")
            // Return empty list instead of fallback content
            emptyList()
        }
    }


}

