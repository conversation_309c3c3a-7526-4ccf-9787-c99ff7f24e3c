package com.peliplat.feature.auth.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.peliplat.feature.auth.domain.model.*
import com.peliplat.feature.auth.domain.usecase.RegisterUseCase
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

/**
 * ViewModel for register screen
 */
class RegisterViewModel(
    private val registerUseCase: RegisterUseCase
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(RegisterUiState())
    val uiState: StateFlow<RegisterUiState> = _uiState.asStateFlow()
    
    private val _authState = MutableStateFlow<AuthState>(AuthState.Idle)
    val authState: StateFlow<AuthState> = _authState.asStateFlow()
    
    fun updateEmail(email: String) {
        _uiState.value = _uiState.value.copy(
            email = email,
            emailError = null
        )
        updateRegisterEnabled()
    }
    
    fun updateUsername(username: String) {
        _uiState.value = _uiState.value.copy(
            username = username,
            usernameError = null
        )
        updateRegisterEnabled()
    }
    
    fun updateDisplayName(displayName: String) {
        _uiState.value = _uiState.value.copy(
            displayName = displayName,
            displayNameError = null
        )
        updateRegisterEnabled()
    }
    
    fun updatePassword(password: String) {
        _uiState.value = _uiState.value.copy(
            password = password,
            passwordError = null
        )
        updateRegisterEnabled()
    }
    
    fun updateConfirmPassword(confirmPassword: String) {
        _uiState.value = _uiState.value.copy(
            confirmPassword = confirmPassword,
            confirmPasswordError = null
        )
        updateRegisterEnabled()
    }
    
    fun updateAcceptTerms(acceptTerms: Boolean) {
        _uiState.value = _uiState.value.copy(
            acceptTerms = acceptTerms,
            termsError = null
        )
        updateRegisterEnabled()
    }
    
    fun register() {
        val currentState = _uiState.value
        
        // Clear previous errors
        _uiState.value = currentState.copy(
            emailError = null,
            usernameError = null,
            displayNameError = null,
            passwordError = null,
            confirmPasswordError = null,
            termsError = null
        )
        
        // Validate input
        val validationErrors = validateInput(currentState)
        if (validationErrors.isNotEmpty()) {
            _uiState.value = currentState.copy(
                emailError = validationErrors["email"],
                usernameError = validationErrors["username"],
                displayNameError = validationErrors["displayName"],
                passwordError = validationErrors["password"],
                confirmPasswordError = validationErrors["confirmPassword"],
                termsError = validationErrors["acceptTerms"]
            )
            return
        }
        
        // Perform registration
        viewModelScope.launch {
            _authState.value = AuthState.Loading
            
            val registrationInfo = RegistrationInfo(
                email = currentState.email.trim(),
                username = currentState.username.trim(),
                displayName = currentState.displayName.trim(),
                password = currentState.password,
                confirmPassword = currentState.confirmPassword,
                acceptTerms = currentState.acceptTerms
            )
            
            when (val result = registerUseCase(registrationInfo)) {
                is AuthResult.Success -> {
                    _authState.value = AuthState.Success(result.data)
                }
                is AuthResult.Error -> {
                    handleAuthError(result.exception)
                }
            }
        }
    }
    
    private fun validateInput(state: RegisterUiState): Map<String, String> {
        val errors = mutableMapOf<String, String>()
        
        // Email validation
        if (state.email.isBlank()) {
            errors["email"] = "Email is required"
        } else if (!isValidEmail(state.email)) {
            errors["email"] = "Please enter a valid email address"
        }
        
        // Username validation
        if (state.username.isBlank()) {
            errors["username"] = "Username is required"
        } else if (state.username.length < 3) {
            errors["username"] = "Username must be at least 3 characters"
        } else if (!isValidUsername(state.username)) {
            errors["username"] = "Username can only contain letters, numbers, and underscores"
        }
        
        // Display name validation
        if (state.displayName.isBlank()) {
            errors["displayName"] = "Display name is required"
        } else if (state.displayName.length > 50) {
            errors["displayName"] = "Display name must be less than 50 characters"
        }
        
        // Password validation
        if (state.password.isBlank()) {
            errors["password"] = "Password is required"
        } else if (state.password.length < 8) {
            errors["password"] = "Password must be at least 8 characters"
        } else if (!isStrongPassword(state.password)) {
            errors["password"] = "Password must contain uppercase, lowercase, and number"
        }
        
        // Confirm password validation
        if (state.confirmPassword.isBlank()) {
            errors["confirmPassword"] = "Please confirm your password"
        } else if (state.password != state.confirmPassword) {
            errors["confirmPassword"] = "Passwords do not match"
        }
        
        // Terms validation
        if (!state.acceptTerms) {
            errors["acceptTerms"] = "You must accept the terms and conditions"
        }
        
        return errors
    }
    
    private fun isValidEmail(email: String): Boolean {
        return email.contains("@") && email.contains(".")
    }
    
    private fun isValidUsername(username: String): Boolean {
        return username.matches(Regex("^[a-zA-Z0-9_]+$"))
    }
    
    private fun isStrongPassword(password: String): Boolean {
        val hasUppercase = password.any { it.isUpperCase() }
        val hasLowercase = password.any { it.isLowerCase() }
        val hasDigit = password.any { it.isDigit() }
        return hasUppercase && hasLowercase && hasDigit
    }
    
    private fun updateRegisterEnabled() {
        val currentState = _uiState.value
        val isEnabled = currentState.email.isNotBlank() && 
                       currentState.username.isNotBlank() &&
                       currentState.displayName.isNotBlank() &&
                       currentState.password.isNotBlank() &&
                       currentState.confirmPassword.isNotBlank() &&
                       currentState.acceptTerms &&
                       isValidEmail(currentState.email)
        
        _uiState.value = currentState.copy(isRegisterEnabled = isEnabled)
    }
    
    private fun handleAuthError(exception: AuthException) {
        when (exception) {
            is AuthException.ValidationError -> {
                _uiState.value = _uiState.value.copy(
                    emailError = exception.errors["email"],
                    usernameError = exception.errors["username"],
                    displayNameError = exception.errors["displayName"],
                    passwordError = exception.errors["password"],
                    confirmPasswordError = exception.errors["confirmPassword"]
                )
                _authState.value = AuthState.Idle
            }
            AuthException.EmailAlreadyExists -> {
                _uiState.value = _uiState.value.copy(
                    emailError = "This email is already registered"
                )
                _authState.value = AuthState.Idle
            }
            AuthException.UsernameAlreadyExists -> {
                _uiState.value = _uiState.value.copy(
                    usernameError = "This username is already taken"
                )
                _authState.value = AuthState.Idle
            }
            AuthException.NetworkError -> {
                _authState.value = AuthState.Error("Network error. Please check your connection.")
            }
            AuthException.ServerError -> {
                _authState.value = AuthState.Error("Server error. Please try again later.")
            }
            else -> {
                _authState.value = AuthState.Error(exception.message ?: "Registration failed")
            }
        }
    }
    
    fun clearError() {
        _authState.value = AuthState.Idle
    }
}

/**
 * UI state for register screen
 */
data class RegisterUiState(
    val email: String = "",
    val username: String = "",
    val displayName: String = "",
    val password: String = "",
    val confirmPassword: String = "",
    val acceptTerms: Boolean = false,
    val emailError: String? = null,
    val usernameError: String? = null,
    val displayNameError: String? = null,
    val passwordError: String? = null,
    val confirmPasswordError: String? = null,
    val termsError: String? = null,
    val isRegisterEnabled: Boolean = false
)
