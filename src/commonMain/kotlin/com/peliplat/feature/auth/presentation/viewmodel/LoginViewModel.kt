package com.peliplat.feature.auth.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.peliplat.feature.auth.domain.model.*
import com.peliplat.feature.auth.domain.usecase.LoginUseCase
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

/**
 * ViewModel for login screen
 */
class LoginViewModel(
    private val loginUseCase: LoginUseCase
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(LoginUiState())
    val uiState: StateFlow<LoginUiState> = _uiState.asStateFlow()
    
    private val _authState = MutableStateFlow<AuthState>(AuthState.Idle)
    val authState: StateFlow<AuthState> = _authState.asStateFlow()
    
    fun updateEmail(email: String) {
        _uiState.value = _uiState.value.copy(
            email = email,
            emailError = null
        )
        updateLoginEnabled()
    }
    
    fun updatePassword(password: String) {
        _uiState.value = _uiState.value.copy(
            password = password,
            passwordError = null
        )
        updateLoginEnabled()
    }
    
    fun updateRememberMe(rememberMe: Boolean) {
        _uiState.value = _uiState.value.copy(rememberMe = rememberMe)
    }
    
    fun login() {
        val currentState = _uiState.value
        
        // Clear previous errors
        _uiState.value = currentState.copy(
            emailError = null,
            passwordError = null
        )
        
        // Validate input
        val validationErrors = validateInput(currentState)
        if (validationErrors.isNotEmpty()) {
            _uiState.value = currentState.copy(
                emailError = validationErrors["email"],
                passwordError = validationErrors["password"]
            )
            return
        }
        
        // Perform login
        viewModelScope.launch {
            _authState.value = AuthState.Loading
            
            val credentials = LoginCredentials(
                email = currentState.email.trim(),
                password = currentState.password,
                rememberMe = currentState.rememberMe
            )
            
            when (val result = loginUseCase(credentials)) {
                is AuthResult.Success -> {
                    _authState.value = AuthState.Success(result.data)
                }
                is AuthResult.Error -> {
                    handleAuthError(result.exception)
                }
            }
        }
    }
    
    private fun validateInput(state: LoginUiState): Map<String, String> {
        val errors = mutableMapOf<String, String>()
        
        if (state.email.isBlank()) {
            errors["email"] = "Email is required"
        } else if (!isValidEmail(state.email)) {
            errors["email"] = "Please enter a valid email address"
        }
        
        if (state.password.isBlank()) {
            errors["password"] = "Password is required"
        } else if (state.password.length < 6) {
            errors["password"] = "Password must be at least 6 characters"
        }
        
        return errors
    }
    
    private fun isValidEmail(email: String): Boolean {
        return email.contains("@") && email.contains(".")
    }
    
    private fun updateLoginEnabled() {
        val currentState = _uiState.value
        val isEnabled = currentState.email.isNotBlank() && 
                       currentState.password.isNotBlank() &&
                       isValidEmail(currentState.email)
        
        _uiState.value = currentState.copy(isLoginEnabled = isEnabled)
    }
    
    private fun handleAuthError(exception: AuthException) {
        when (exception) {
            is AuthException.ValidationError -> {
                _uiState.value = _uiState.value.copy(
                    emailError = exception.errors["email"],
                    passwordError = exception.errors["password"]
                )
                _authState.value = AuthState.Idle
            }
            AuthException.InvalidCredentials -> {
                _authState.value = AuthState.Error("Invalid email or password")
            }
            AuthException.NetworkError -> {
                _authState.value = AuthState.Error("Network error. Please check your connection.")
            }
            AuthException.ServerError -> {
                _authState.value = AuthState.Error("Server error. Please try again later.")
            }
            else -> {
                _authState.value = AuthState.Error(exception.message ?: "Login failed")
            }
        }
    }
    
    fun clearError() {
        _authState.value = AuthState.Idle
    }
}

/**
 * UI state for login screen
 */
data class LoginUiState(
    val email: String = "",
    val password: String = "",
    val rememberMe: Boolean = false,
    val emailError: String? = null,
    val passwordError: String? = null,
    val isLoginEnabled: Boolean = false
)
