package com.peliplat.feature.auth.domain.usecase

import com.peliplat.feature.auth.domain.model.*
import com.peliplat.feature.auth.domain.repository.AuthRepository

/**
 * Use case for user registration with validation
 */
class RegisterUseCase(
    private val authRepository: AuthRepository
) {
    suspend operator fun invoke(registrationInfo: RegistrationInfo): AuthResult<UserProfile> {
        // Validate input
        val validationResult = validateRegistrationInfo(registrationInfo)
        if (validationResult != null) {
            return AuthResult.Error(validationResult)
        }
        
        // Perform registration
        return authRepository.register(registrationInfo)
    }
    
    private fun validateRegistrationInfo(info: RegistrationInfo): AuthException? {
        val errors = mutableMapOf<String, String>()
        
        // Email validation
        when {
            info.email.isBlank() -> errors["email"] = "Email is required"
            !isValidEmail(info.email) -> errors["email"] = "Invalid email format"
        }
        
        // Username validation
        when {
            info.username.isBlank() -> errors["username"] = "Username is required"
            info.username.length < 3 -> errors["username"] = "Username must be at least 3 characters"
            info.username.length > 20 -> errors["username"] = "Username must be less than 20 characters"
            !isValidUsername(info.username) -> errors["username"] = "Username can only contain letters, numbers, and underscores"
        }
        
        // Display name validation
        when {
            info.displayName.isBlank() -> errors["displayName"] = "Display name is required"
            info.displayName.length > 50 -> errors["displayName"] = "Display name must be less than 50 characters"
        }
        
        // Password validation
        when {
            info.password.isBlank() -> errors["password"] = "Password is required"
            info.password.length < 8 -> errors["password"] = "Password must be at least 8 characters"
            !isStrongPassword(info.password) -> errors["password"] = "Password must contain at least one uppercase letter, one lowercase letter, and one number"
        }
        
        // Confirm password validation
        if (info.password != info.confirmPassword) {
            errors["confirmPassword"] = "Passwords do not match"
        }
        
        // Terms acceptance validation
        if (!info.acceptTerms) {
            errors["acceptTerms"] = "You must accept the terms and conditions"
        }
        
        return if (errors.isNotEmpty()) {
            AuthException.ValidationError(errors)
        } else {
            null
        }
    }
    
    private fun isValidEmail(email: String): Boolean {
        val emailRegex = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$".toRegex()
        return emailRegex.matches(email)
    }
    
    private fun isValidUsername(username: String): Boolean {
        val usernameRegex = "^[a-zA-Z0-9_]+$".toRegex()
        return usernameRegex.matches(username)
    }
    
    private fun isStrongPassword(password: String): Boolean {
        val hasUppercase = password.any { it.isUpperCase() }
        val hasLowercase = password.any { it.isLowerCase() }
        val hasDigit = password.any { it.isDigit() }
        return hasUppercase && hasLowercase && hasDigit
    }
}
