package com.peliplat.feature.auth.domain.usecase

import com.peliplat.feature.auth.domain.model.*
import com.peliplat.feature.auth.domain.repository.AuthRepository

/**
 * Use case for user login with validation
 */
class LoginUseCase(
    private val authRepository: AuthRepository
) {
    suspend operator fun invoke(credentials: LoginCredentials): AuthResult<UserProfile> {
        // Validate input
        val validationResult = validateCredentials(credentials)
        if (validationResult != null) {
            return AuthResult.Error(validationResult)
        }
        
        // Perform login
        return authRepository.login(credentials)
    }
    
    private fun validateCredentials(credentials: LoginCredentials): AuthException? {
        return when {
            credentials.email.isBlank() -> AuthException.ValidationError(
                mapOf("email" to "Email is required")
            )
            !isValidEmail(credentials.email) -> AuthException.InvalidEmail
            credentials.password.isBlank() -> AuthException.ValidationError(
                mapOf("password" to "Password is required")
            )
            credentials.password.length < 6 -> AuthException.ValidationError(
                mapOf("password" to "Password must be at least 6 characters")
            )
            else -> null
        }
    }
    
    private fun isValidEmail(email: String): Boolean {
        return email.contains("@") && email.contains(".")
    }
}
