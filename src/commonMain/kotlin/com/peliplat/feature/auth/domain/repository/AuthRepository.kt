package com.peliplat.feature.auth.domain.repository

import com.peliplat.feature.auth.domain.model.*
import kotlinx.coroutines.flow.Flow

/**
 * Authentication repository interface defining all authentication operations
 */
interface AuthRepository {
    
    /**
     * Current authentication state as a flow
     */
    val authState: Flow<AuthState>
    
    /**
     * Current session information as a flow
     */
    val sessionInfo: Flow<SessionInfo>
    
    /**
     * Login with email and password
     */
    suspend fun login(credentials: LoginCredentials): AuthResult<UserProfile>
    
    /**
     * Register a new user account
     */
    suspend fun register(registrationInfo: RegistrationInfo): AuthResult<UserProfile>
    
    /**
     * Login with OAuth provider
     */
    suspend fun loginWithOAuth(oauthRequest: OAuthRequest): AuthResult<UserProfile>
    
    /**
     * Refresh authentication token
     */
    suspend fun refreshToken(): AuthResult<TokenResponse>
    
    /**
     * Logout current user
     */
    suspend fun logout(): AuthResult<Unit>
    
    /**
     * Request password reset
     */
    suspend fun requestPasswordReset(email: String): AuthResult<Unit>
    
    /**
     * Confirm password reset with token
     */
    suspend fun confirmPasswordReset(confirmation: PasswordResetConfirmation): AuthResult<Unit>
    
    /**
     * Verify email with token
     */
    suspend fun verifyEmail(token: String): AuthResult<Unit>
    
    /**
     * Resend email verification
     */
    suspend fun resendEmailVerification(): AuthResult<Unit>
    
    /**
     * Get current user profile
     */
    suspend fun getCurrentUser(): AuthResult<UserProfile>
    
    /**
     * Update user profile
     */
    suspend fun updateProfile(userProfile: UserProfile): AuthResult<UserProfile>
    
    /**
     * Update user preferences
     */
    suspend fun updatePreferences(preferences: UserPreferences): AuthResult<UserPreferences>
    
    /**
     * Change password
     */
    suspend fun changePassword(currentPassword: String, newPassword: String): AuthResult<Unit>
    
    /**
     * Delete user account
     */
    suspend fun deleteAccount(password: String): AuthResult<Unit>
    
    /**
     * Check if user is authenticated
     */
    suspend fun isAuthenticated(): Boolean
    
    /**
     * Get valid access token (refresh if needed)
     */
    suspend fun getValidAccessToken(): String?
    
    /**
     * Clear all authentication data
     */
    suspend fun clearAuthData()
}
