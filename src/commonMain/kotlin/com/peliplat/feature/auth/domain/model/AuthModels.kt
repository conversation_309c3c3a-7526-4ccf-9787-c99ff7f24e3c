package com.peliplat.feature.auth.domain.model

import kotlinx.serialization.Serializable

/**
 * User authentication credentials for login
 */
@Serializable
data class LoginCredentials(
    val email: String,
    val password: String,
    val rememberMe: Boolean = false
)

/**
 * User registration information
 */
@Serializable
data class RegistrationInfo(
    val email: String,
    val username: String,
    val displayName: String,
    val password: String,
    val confirmPassword: String,
    val acceptTerms: Boolean = false
)

/**
 * Authentication response from server
 */
@Serializable
data class AuthenticationResponse(
    val accessToken: String,
    val refreshToken: String,
    val expiresIn: Long,
    val tokenType: String = "Bearer",
    val user: UserProfile
)

/**
 * Token refresh response
 */
@Serializable
data class TokenResponse(
    val accessToken: String,
    val refreshToken: String,
    val expiresIn: Long,
    val expiresAt: Long = System.currentTimeMillis() + (expiresIn * 1000)
)

/**
 * User profile information
 */
@Serializable
data class UserProfile(
    val id: String,
    val email: String,
    val username: String,
    val displayName: String,
    val avatarUrl: String? = null,
    val bio: String? = null,
    val createdAt: String,
    val updatedAt: String,
    val isVerified: Boolean = false,
    val preferences: UserPreferences = UserPreferences()
)

/**
 * User preferences and settings
 */
@Serializable
data class UserPreferences(
    val theme: ThemePreference = ThemePreference.SYSTEM,
    val language: String = "en",
    val notificationsEnabled: Boolean = true,
    val emailNotifications: Boolean = true,
    val pushNotifications: Boolean = true,
    val privacySettings: PrivacySettings = PrivacySettings()
)

/**
 * Privacy settings for user
 */
@Serializable
data class PrivacySettings(
    val profileVisibility: ProfileVisibility = ProfileVisibility.PUBLIC,
    val showEmail: Boolean = false,
    val allowFollowing: Boolean = true,
    val allowDirectMessages: Boolean = true
)

/**
 * Theme preference options
 */
@Serializable
enum class ThemePreference {
    LIGHT, DARK, SYSTEM
}

/**
 * Profile visibility options
 */
@Serializable
enum class ProfileVisibility {
    PUBLIC, FRIENDS_ONLY, PRIVATE
}

/**
 * OAuth provider types
 */
enum class OAuthProvider {
    GOOGLE, APPLE, FACEBOOK
}

/**
 * OAuth authentication request
 */
@Serializable
data class OAuthRequest(
    val provider: String,
    val token: String,
    val email: String? = null,
    val name: String? = null
)

/**
 * Password reset request
 */
@Serializable
data class PasswordResetRequest(
    val email: String
)

/**
 * Password reset confirmation
 */
@Serializable
data class PasswordResetConfirmation(
    val token: String,
    val newPassword: String,
    val confirmPassword: String
)

/**
 * Email verification request
 */
@Serializable
data class EmailVerificationRequest(
    val token: String
)

/**
 * Authentication state for UI
 */
sealed class AuthState {
    object Idle : AuthState()
    object Loading : AuthState()
    data class Success(val user: UserProfile) : AuthState()
    data class Error(val message: String) : AuthState()
}

/**
 * Authentication result wrapper
 */
sealed class AuthResult<out T> {
    data class Success<T>(val data: T) : AuthResult<T>()
    data class Error(val exception: AuthException) : AuthResult<Nothing>()
}

/**
 * Authentication exceptions
 */
sealed class AuthException(message: String) : Exception(message) {
    object InvalidCredentials : AuthException("Invalid email or password")
    object UserNotFound : AuthException("User not found")
    object EmailAlreadyExists : AuthException("Email already registered")
    object UsernameAlreadyExists : AuthException("Username already taken")
    object WeakPassword : AuthException("Password is too weak")
    object InvalidEmail : AuthException("Invalid email format")
    object TokenExpired : AuthException("Authentication token expired")
    object NetworkError : AuthException("Network connection error")
    object ServerError : AuthException("Server error occurred")
    data class ValidationError(val errors: Map<String, String>) : AuthException("Validation failed")
    data class Unknown(val originalMessage: String) : AuthException(originalMessage)
}

/**
 * Biometric authentication result
 */
sealed class BiometricResult {
    object Success : BiometricResult()
    object Failed : BiometricResult()
    object Cancelled : BiometricResult()
    object NotAvailable : BiometricResult()
    object NotEnrolled : BiometricResult()
    data class Error(val message: String) : BiometricResult()
}

/**
 * Session information
 */
data class SessionInfo(
    val isLoggedIn: Boolean,
    val user: UserProfile? = null,
    val accessToken: String? = null,
    val refreshToken: String? = null,
    val expiresAt: Long? = null
) {
    val isTokenValid: Boolean
        get() = expiresAt?.let { it > System.currentTimeMillis() } ?: false
}
