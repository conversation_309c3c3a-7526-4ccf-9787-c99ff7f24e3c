package com.peliplat.feature.auth.data.remote

import com.peliplat.feature.auth.domain.model.*
import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.request.*
import io.ktor.http.*

/**
 * Authentication API interface for network operations
 */
interface AuthApi {
    suspend fun login(credentials: LoginCredentials): AuthenticationResponse
    suspend fun register(registrationInfo: RegistrationInfo): AuthenticationResponse
    suspend fun loginWithOAuth(oauthRequest: OAuthRequest): AuthenticationResponse
    suspend fun refreshToken(refreshToken: String): TokenResponse
    suspend fun logout(): Unit
    suspend fun requestPasswordReset(email: String): Unit
    suspend fun confirmPasswordReset(confirmation: PasswordResetConfirmation): Unit
    suspend fun verifyEmail(token: String): Unit
    suspend fun resendEmailVerification(): Unit
    suspend fun getCurrentUser(): UserProfile
    suspend fun updateProfile(userProfile: UserProfile): UserProfile
    suspend fun updatePreferences(preferences: UserPreferences): UserPreferences
    suspend fun changePassword(currentPassword: String, newPassword: String): Unit
    suspend fun deleteAccount(password: String): Unit
}

/**
 * Implementation of AuthApi using Ktor client
 */
class AuthApiImpl(
    private val httpClient: HttpClient,
    private val baseUrl: String = "https://api.peliplat.com"
) : AuthApi {
    
    override suspend fun login(credentials: LoginCredentials): AuthenticationResponse {
        return httpClient.post("$baseUrl/auth/login") {
            contentType(ContentType.Application.Json)
            setBody(credentials)
        }.body()
    }
    
    override suspend fun register(registrationInfo: RegistrationInfo): AuthenticationResponse {
        return httpClient.post("$baseUrl/auth/register") {
            contentType(ContentType.Application.Json)
            setBody(registrationInfo)
        }.body()
    }
    
    override suspend fun loginWithOAuth(oauthRequest: OAuthRequest): AuthenticationResponse {
        return httpClient.post("$baseUrl/auth/oauth") {
            contentType(ContentType.Application.Json)
            setBody(oauthRequest)
        }.body()
    }
    
    override suspend fun refreshToken(refreshToken: String): TokenResponse {
        return httpClient.post("$baseUrl/auth/refresh") {
            contentType(ContentType.Application.Json)
            setBody(mapOf("refreshToken" to refreshToken))
        }.body()
    }
    
    override suspend fun logout(): Unit {
        httpClient.post("$baseUrl/auth/logout") {
            contentType(ContentType.Application.Json)
        }
    }
    
    override suspend fun requestPasswordReset(email: String): Unit {
        httpClient.post("$baseUrl/auth/password-reset") {
            contentType(ContentType.Application.Json)
            setBody(PasswordResetRequest(email))
        }
    }
    
    override suspend fun confirmPasswordReset(confirmation: PasswordResetConfirmation): Unit {
        httpClient.post("$baseUrl/auth/password-reset/confirm") {
            contentType(ContentType.Application.Json)
            setBody(confirmation)
        }
    }
    
    override suspend fun verifyEmail(token: String): Unit {
        httpClient.post("$baseUrl/auth/verify-email") {
            contentType(ContentType.Application.Json)
            setBody(EmailVerificationRequest(token))
        }
    }
    
    override suspend fun resendEmailVerification(): Unit {
        httpClient.post("$baseUrl/auth/verify-email/resend") {
            contentType(ContentType.Application.Json)
        }
    }
    
    override suspend fun getCurrentUser(): UserProfile {
        return httpClient.get("$baseUrl/auth/me").body()
    }
    
    override suspend fun updateProfile(userProfile: UserProfile): UserProfile {
        return httpClient.put("$baseUrl/auth/profile") {
            contentType(ContentType.Application.Json)
            setBody(userProfile)
        }.body()
    }
    
    override suspend fun updatePreferences(preferences: UserPreferences): UserPreferences {
        return httpClient.put("$baseUrl/auth/preferences") {
            contentType(ContentType.Application.Json)
            setBody(preferences)
        }.body()
    }
    
    override suspend fun changePassword(currentPassword: String, newPassword: String): Unit {
        httpClient.put("$baseUrl/auth/password") {
            contentType(ContentType.Application.Json)
            setBody(mapOf(
                "currentPassword" to currentPassword,
                "newPassword" to newPassword
            ))
        }
    }
    
    override suspend fun deleteAccount(password: String): Unit {
        httpClient.delete("$baseUrl/auth/account") {
            contentType(ContentType.Application.Json)
            setBody(mapOf("password" to password))
        }
    }
}
