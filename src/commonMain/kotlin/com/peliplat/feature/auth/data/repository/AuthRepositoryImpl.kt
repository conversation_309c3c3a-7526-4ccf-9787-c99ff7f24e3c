package com.peliplat.feature.auth.data.repository

import com.peliplat.core.security.SecureStorage
import com.peliplat.core.security.SecureStorageKeys
import com.peliplat.core.security.TokenManager
import com.peliplat.feature.auth.data.remote.AuthApi
import com.peliplat.feature.auth.domain.model.*
import com.peliplat.feature.auth.domain.repository.AuthRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.serialization.json.Json

/**
 * Implementation of AuthRepository
 */
class AuthRepositoryImpl(
    private val authApi: AuthApi,
    private val tokenManager: TokenManager,
    private val secureStorage: SecureStorage
) : AuthRepository {
    
    private val _authState = MutableStateFlow<AuthState>(AuthState.Idle)
    override val authState: Flow<AuthState> = _authState.asStateFlow()
    
    private val _sessionInfo = MutableStateFlow(SessionInfo(isLoggedIn = false))
    override val sessionInfo: Flow<SessionInfo> = _sessionInfo.asStateFlow()
    
    init {
        // Set up token refresh callback
        tokenManager.setRefreshTokenCallback { refreshToken ->
            try {
                authApi.refreshToken(refreshToken)
            } catch (e: Exception) {
                null
            }
        }
    }
    
    override suspend fun login(credentials: LoginCredentials): AuthResult<UserProfile> {
        return try {
            _authState.value = AuthState.Loading
            
            val response = authApi.login(credentials)
            
            // Store tokens
            val tokenResponse = TokenResponse(
                accessToken = response.accessToken,
                refreshToken = response.refreshToken,
                expiresIn = response.expiresIn
            )
            tokenManager.storeTokens(tokenResponse)
            
            // Store user info
            storeUserInfo(response.user, credentials.rememberMe)
            
            // Update state
            _authState.value = AuthState.Success(response.user)
            updateSessionInfo(response.user)
            
            AuthResult.Success(response.user)
        } catch (e: Exception) {
            val authException = mapException(e)
            _authState.value = AuthState.Error(authException.message ?: "Login failed")
            AuthResult.Error(authException)
        }
    }
    
    override suspend fun register(registrationInfo: RegistrationInfo): AuthResult<UserProfile> {
        return try {
            _authState.value = AuthState.Loading
            
            val response = authApi.register(registrationInfo)
            
            // Store tokens
            val tokenResponse = TokenResponse(
                accessToken = response.accessToken,
                refreshToken = response.refreshToken,
                expiresIn = response.expiresIn
            )
            tokenManager.storeTokens(tokenResponse)
            
            // Store user info
            storeUserInfo(response.user, false)
            
            // Update state
            _authState.value = AuthState.Success(response.user)
            updateSessionInfo(response.user)
            
            AuthResult.Success(response.user)
        } catch (e: Exception) {
            val authException = mapException(e)
            _authState.value = AuthState.Error(authException.message ?: "Registration failed")
            AuthResult.Error(authException)
        }
    }
    
    override suspend fun loginWithOAuth(oauthRequest: OAuthRequest): AuthResult<UserProfile> {
        return try {
            _authState.value = AuthState.Loading
            
            val response = authApi.loginWithOAuth(oauthRequest)
            
            // Store tokens
            val tokenResponse = TokenResponse(
                accessToken = response.accessToken,
                refreshToken = response.refreshToken,
                expiresIn = response.expiresIn
            )
            tokenManager.storeTokens(tokenResponse)
            
            // Store user info
            storeUserInfo(response.user, true)
            
            // Update state
            _authState.value = AuthState.Success(response.user)
            updateSessionInfo(response.user)
            
            AuthResult.Success(response.user)
        } catch (e: Exception) {
            val authException = mapException(e)
            _authState.value = AuthState.Error(authException.message ?: "OAuth login failed")
            AuthResult.Error(authException)
        }
    }
    
    override suspend fun refreshToken(): AuthResult<TokenResponse> {
        return try {
            val refreshToken = tokenManager.getRefreshToken()
                ?: return AuthResult.Error(AuthException.TokenExpired)
            
            val response = authApi.refreshToken(refreshToken)
            tokenManager.storeTokens(response)
            
            AuthResult.Success(response)
        } catch (e: Exception) {
            val authException = mapException(e)
            AuthResult.Error(authException)
        }
    }
    
    override suspend fun logout(): AuthResult<Unit> {
        return try {
            // Call logout API if we have a valid token
            if (tokenManager.hasTokens()) {
                try {
                    authApi.logout()
                } catch (e: Exception) {
                    // Continue with local logout even if API call fails
                }
            }
            
            // Clear all auth data
            clearAuthData()
            
            // Update state
            _authState.value = AuthState.Idle
            _sessionInfo.value = SessionInfo(isLoggedIn = false)
            
            AuthResult.Success(Unit)
        } catch (e: Exception) {
            val authException = mapException(e)
            AuthResult.Error(authException)
        }
    }
    
    override suspend fun requestPasswordReset(email: String): AuthResult<Unit> {
        return try {
            authApi.requestPasswordReset(email)
            AuthResult.Success(Unit)
        } catch (e: Exception) {
            val authException = mapException(e)
            AuthResult.Error(authException)
        }
    }
    
    override suspend fun confirmPasswordReset(confirmation: PasswordResetConfirmation): AuthResult<Unit> {
        return try {
            authApi.confirmPasswordReset(confirmation)
            AuthResult.Success(Unit)
        } catch (e: Exception) {
            val authException = mapException(e)
            AuthResult.Error(authException)
        }
    }
    
    override suspend fun verifyEmail(token: String): AuthResult<Unit> {
        return try {
            authApi.verifyEmail(token)
            AuthResult.Success(Unit)
        } catch (e: Exception) {
            val authException = mapException(e)
            AuthResult.Error(authException)
        }
    }
    
    override suspend fun resendEmailVerification(): AuthResult<Unit> {
        return try {
            authApi.resendEmailVerification()
            AuthResult.Success(Unit)
        } catch (e: Exception) {
            val authException = mapException(e)
            AuthResult.Error(authException)
        }
    }
    
    override suspend fun getCurrentUser(): AuthResult<UserProfile> {
        return try {
            val user = authApi.getCurrentUser()
            storeUserInfo(user, secureStorage.contains(SecureStorageKeys.REMEMBER_ME))
            updateSessionInfo(user)
            AuthResult.Success(user)
        } catch (e: Exception) {
            val authException = mapException(e)
            AuthResult.Error(authException)
        }
    }
    
    override suspend fun updateProfile(userProfile: UserProfile): AuthResult<UserProfile> {
        return try {
            val updatedUser = authApi.updateProfile(userProfile)
            storeUserInfo(updatedUser, secureStorage.contains(SecureStorageKeys.REMEMBER_ME))
            updateSessionInfo(updatedUser)
            AuthResult.Success(updatedUser)
        } catch (e: Exception) {
            val authException = mapException(e)
            AuthResult.Error(authException)
        }
    }
    
    override suspend fun updatePreferences(preferences: UserPreferences): AuthResult<UserPreferences> {
        return try {
            val updatedPreferences = authApi.updatePreferences(preferences)
            AuthResult.Success(updatedPreferences)
        } catch (e: Exception) {
            val authException = mapException(e)
            AuthResult.Error(authException)
        }
    }
    
    override suspend fun changePassword(currentPassword: String, newPassword: String): AuthResult<Unit> {
        return try {
            authApi.changePassword(currentPassword, newPassword)
            AuthResult.Success(Unit)
        } catch (e: Exception) {
            val authException = mapException(e)
            AuthResult.Error(authException)
        }
    }
    
    override suspend fun deleteAccount(password: String): AuthResult<Unit> {
        return try {
            authApi.deleteAccount(password)
            clearAuthData()
            _authState.value = AuthState.Idle
            _sessionInfo.value = SessionInfo(isLoggedIn = false)
            AuthResult.Success(Unit)
        } catch (e: Exception) {
            val authException = mapException(e)
            AuthResult.Error(authException)
        }
    }
    
    override suspend fun isAuthenticated(): Boolean {
        return tokenManager.hasTokens() && tokenManager.isTokenValid()
    }
    
    override suspend fun getValidAccessToken(): String? {
        return tokenManager.getValidAccessToken()
    }
    
    override suspend fun clearAuthData() {
        tokenManager.clearTokens()
        secureStorage.remove(SecureStorageKeys.USER_ID)
        secureStorage.remove(SecureStorageKeys.USER_EMAIL)
        secureStorage.remove(SecureStorageKeys.REMEMBER_ME)
    }
    
    private suspend fun storeUserInfo(user: UserProfile, rememberMe: Boolean) {
        secureStorage.store(SecureStorageKeys.USER_ID, user.id)
        secureStorage.store(SecureStorageKeys.USER_EMAIL, user.email)
        if (rememberMe) {
            secureStorage.store(SecureStorageKeys.REMEMBER_ME, "true")
        }
    }
    
    private suspend fun updateSessionInfo(user: UserProfile) {
        val accessToken = tokenManager.getAccessToken()
        val refreshToken = tokenManager.getRefreshToken()
        val expiresAt = tokenManager.getTokenExpiry()
        
        _sessionInfo.value = SessionInfo(
            isLoggedIn = true,
            user = user,
            accessToken = accessToken,
            refreshToken = refreshToken,
            expiresAt = expiresAt
        )
    }
    
    private fun mapException(exception: Exception): AuthException {
        return when {
            exception.message?.contains("401") == true -> AuthException.InvalidCredentials
            exception.message?.contains("404") == true -> AuthException.UserNotFound
            exception.message?.contains("409") == true -> AuthException.EmailAlreadyExists
            exception.message?.contains("network") == true -> AuthException.NetworkError
            exception.message?.contains("server") == true -> AuthException.ServerError
            else -> AuthException.Unknown(exception.message ?: "Unknown error")
        }
    }
}
